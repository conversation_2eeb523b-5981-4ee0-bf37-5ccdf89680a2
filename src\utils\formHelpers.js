// Helpers para formularios

import moment from 'moment'

// Helper para generar opciones de select
export const generateSelectOptions = (items, valueKey = 'id', labelKey = 'name', emptyOption = null) => {
  const options = items.map(item => ({
    value: item[valueKey],
    label: item[labelKey],
    data: item
  }))
  
  if (emptyOption) {
    options.unshift({
      value: '',
      label: emptyOption,
      data: null
    })
  }
  
  return options
}

// Helper para generar opciones de días del mes
export const generateDayOptions = (maxDay = 31) => {
  const options = []
  for (let i = 1; i <= maxDay; i++) {
    options.push({
      value: i,
      label: `Día ${i} de cada mes`
    })
  }
  return options
}

// Helper para generar opciones de años
export const generateYearOptions = (startYear = null, endYear = null, reverse = true) => {
  const currentYear = new Date().getFullYear()
  const start = startYear || (currentYear - 10)
  const end = endYear || (currentYear + 5)
  
  const options = []
  for (let year = start; year <= end; year++) {
    options.push({
      value: year,
      label: year.toString()
    })
  }
  
  return reverse ? options.reverse() : options
}

// Helper para generar opciones de meses
export const generateMonthOptions = () => {
  const months = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ]
  
  return months.map((month, index) => ({
    value: index + 1,
    label: month
  }))
}

// Helper para resetear formulario
export const resetForm = (formRef, initialValues = {}) => {
  if (formRef.value) {
    Object.keys(formRef.value).forEach(key => {
      if (initialValues.hasOwnProperty(key)) {
        formRef.value[key] = initialValues[key]
      } else if (typeof formRef.value[key] === 'string') {
        formRef.value[key] = ''
      } else if (typeof formRef.value[key] === 'number') {
        formRef.value[key] = null
      } else if (typeof formRef.value[key] === 'boolean') {
        formRef.value[key] = false
      } else if (Array.isArray(formRef.value[key])) {
        formRef.value[key] = []
      } else if (typeof formRef.value[key] === 'object') {
        formRef.value[key] = {}
      }
    })
  }
}

// Helper para validar formulario completo
export const validateFormFields = (form, validators, errors) => {
  const newErrors = {}
  
  Object.keys(validators).forEach(field => {
    const validator = validators[field]
    const value = form[field]
    
    if (typeof validator === 'function') {
      const result = validator(value, form)
      if (result !== true) {
        newErrors[field] = result
      }
    } else if (Array.isArray(validator)) {
      // Múltiples validadores para un campo
      for (const v of validator) {
        const result = v(value, form)
        if (result !== true) {
          newErrors[field] = result
          break
        }
      }
    }
  })
  
  // Actualizar errores
  Object.keys(errors.value).forEach(key => {
    delete errors.value[key]
  })
  Object.assign(errors.value, newErrors)
  
  return Object.keys(newErrors).length === 0
}

// Helper para manejar cambios de archivo
export const handleFileUpload = (event, allowedTypes = [], maxSize = 10 * 1024 * 1024) => {
  const file = event.target.files[0]
  
  if (!file) {
    return { success: false, error: 'No se seleccionó ningún archivo' }
  }
  
  // Validar tipo de archivo
  if (allowedTypes.length > 0) {
    const fileExt = file.name.split('.').pop().toLowerCase()
    if (!allowedTypes.includes(fileExt)) {
      return {
        success: false,
        error: `Tipo de archivo no permitido. Formatos permitidos: ${allowedTypes.join(', ').toUpperCase()}`
      }
    }
  }
  
  // Validar tamaño
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024))
    return {
      success: false,
      error: `El archivo es demasiado grande. Tamaño máximo: ${maxSizeMB}MB`
    }
  }
  
  return { success: true, file }
}

// Helper para formatear datos antes de enviar
export const prepareFormData = (form, transformations = {}) => {
  const data = { ...form }
  
  Object.keys(transformations).forEach(field => {
    const transform = transformations[field]
    
    if (typeof transform === 'function') {
      data[field] = transform(data[field])
    } else if (transform === 'number') {
      data[field] = data[field] ? parseFloat(data[field]) : null
    } else if (transform === 'integer') {
      data[field] = data[field] ? parseInt(data[field]) : null
    } else if (transform === 'boolean') {
      data[field] = Boolean(data[field])
    } else if (transform === 'date') {
      data[field] = data[field] ? moment(data[field]).format('YYYY-MM-DD') : null
    } else if (transform === 'datetime') {
      data[field] = data[field] ? moment(data[field]).toISOString() : null
    }
  })
  
  return data
}

// Helper para calcular fechas automáticamente
export const calculateDates = {
  // Calcular fecha de fin basada en duración
  endDateFromDuration: (startDate, months) => {
    if (!startDate || !months) return null
    return moment(startDate).add(months, 'months').subtract(1, 'day').format('YYYY-MM-DD')
  },
  
  // Calcular duración entre fechas
  durationBetweenDates: (startDate, endDate, unit = 'months') => {
    if (!startDate || !endDate) return 0
    return moment(endDate).diff(moment(startDate), unit)
  },
  
  // Obtener primer día del mes
  firstDayOfMonth: (date = null) => {
    const targetDate = date ? moment(date) : moment()
    return targetDate.startOf('month').format('YYYY-MM-DD')
  },
  
  // Obtener último día del mes
  lastDayOfMonth: (date = null) => {
    const targetDate = date ? moment(date) : moment()
    return targetDate.endOf('month').format('YYYY-MM-DD')
  },
  
  // Calcular próxima fecha de pago
  nextPaymentDate: (lastPaymentDate, dayOfMonth = 1) => {
    const lastPayment = moment(lastPaymentDate)
    const nextMonth = lastPayment.add(1, 'month')
    return nextMonth.date(dayOfMonth).format('YYYY-MM-DD')
  }
}

// Helper para manejar estados de carga
export const createLoadingState = () => {
  const isLoading = ref(false)
  const error = ref(null)
  
  const withLoading = async (asyncFn) => {
    isLoading.value = true
    error.value = null
    
    try {
      const result = await asyncFn()
      return result
    } catch (err) {
      error.value = err.message || 'Ha ocurrido un error'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  return {
    isLoading,
    error,
    withLoading
  }
}

// Helper para debounce en búsquedas
export const createDebounce = (fn, delay = 300) => {
  let timeoutId
  
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(null, args), delay)
  }
}

// Helper para paginación
export const createPagination = (totalItems, itemsPerPage = 10) => {
  const currentPage = ref(1)
  
  const totalPages = computed(() => Math.ceil(totalItems.value / itemsPerPage))
  const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)
  const endIndex = computed(() => Math.min(startIndex.value + itemsPerPage, totalItems.value))
  
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }
  
  const nextPage = () => goToPage(currentPage.value + 1)
  const prevPage = () => goToPage(currentPage.value - 1)
  
  return {
    currentPage,
    totalPages,
    startIndex,
    endIndex,
    goToPage,
    nextPage,
    prevPage
  }
}

// Helper para filtros de búsqueda
export const createSearchFilter = (items, searchFields = ['name']) => {
  const searchTerm = ref('')
  
  const filteredItems = computed(() => {
    if (!searchTerm.value) return items.value
    
    const term = searchTerm.value.toLowerCase()
    
    return items.value.filter(item => {
      return searchFields.some(field => {
        const value = getNestedProperty(item, field)
        return value && value.toString().toLowerCase().includes(term)
      })
    })
  })
  
  return {
    searchTerm,
    filteredItems
  }
}

// Helper para obtener propiedades anidadas
const getNestedProperty = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// Helper para validaciones en tiempo real
export const createRealTimeValidation = (form, validators, debounceDelay = 500) => {
  const errors = ref({})
  const isValidating = ref(false)
  
  const debouncedValidate = createDebounce(async (field, value) => {
    isValidating.value = true
    
    try {
      const validator = validators[field]
      if (validator) {
        const result = await validator(value, form.value)
        if (result === true) {
          delete errors.value[field]
        } else {
          errors.value[field] = result
        }
      }
    } finally {
      isValidating.value = false
    }
  }, debounceDelay)
  
  const validateField = (field) => {
    const value = form.value[field]
    debouncedValidate(field, value)
  }
  
  return {
    errors,
    isValidating,
    validateField
  }
}

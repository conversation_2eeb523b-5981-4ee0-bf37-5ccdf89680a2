import api from './api'

export const documentosService = {
  // Obtener todos los documentos
  getAll(params = {}) {
    return api.get('/documentos', { params })
  },

  // Obtener documento por ID
  getById(id) {
    return api.get(`/documentos/${id}`)
  },

  // Subir nuevo documento
  upload(formData) {
    return api.post('/documentos', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // Actualizar documento
  update(id, data) {
    return api.put(`/documentos/${id}`, data)
  },

  // Eliminar documento
  delete(id) {
    return api.delete(`/documentos/${id}`)
  },

  // Descargar documento
  download(id) {
    return api.get(`/documentos/${id}/download`, {
      responseType: 'blob'
    })
  },

  // Obtener documentos por propiedad
  getByPropiedad(tipo, propiedadId) {
    return api.get(`/documentos-propiedad/${tipo}/${propiedadId}`)
  },

  // Obtener documentos por tipo
  getByTipo(tipo) {
    return api.get(`/documentos-tipo/${tipo}`)
  },

  // Procesar documento
  procesar(id) {
    return api.patch(`/documentos/${id}/procesar`)
  },

  // Archivar documento
  archivar(id) {
    return api.patch(`/documentos/${id}/archivar`)
  }
}

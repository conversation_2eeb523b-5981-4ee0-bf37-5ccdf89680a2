// Formateo de moneda
export const formatCurrency = (amount, currency = 'EUR', locale = 'es-ES') => {
  if (amount === null || amount === undefined) return '€0,00'
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Formateo de fechas
export const formatDate = (date, options = {}) => {
  if (!date) return '-'
  
  const defaultOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }
  
  const formatOptions = { ...defaultOptions, ...options }
  
  return new Date(date).toLocaleDateString('es-ES', formatOptions)
}

// Formateo de fecha y hora
export const formatDateTime = (date) => {
  if (!date) return '-'
  
  return new Date(date).toLocaleString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Formateo de tiempo relativo
export const formatRelativeTime = (date) => {
  if (!date) return '-'
  
  const now = new Date()
  const time = new Date(date)
  const diff = now - time
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)
  
  if (seconds < 60) return 'Hace un momento'
  if (minutes < 60) return `Hace ${minutes} min`
  if (hours < 24) return `Hace ${hours} h`
  if (days < 30) return `Hace ${days} días`
  if (months < 12) return `Hace ${months} meses`
  return `Hace ${years} años`
}

// Formateo de números
export const formatNumber = (number, decimals = 0) => {
  if (number === null || number === undefined) return '0'
  
  return new Intl.NumberFormat('es-ES', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number)
}

// Formateo de porcentajes
export const formatPercentage = (value, total, decimals = 1) => {
  if (!total || total === 0) return '0%'
  
  const percentage = (value / total) * 100
  return `${percentage.toFixed(decimals)}%`
}

// Formateo de tamaño de archivo
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Capitalizar primera letra
export const capitalize = (str) => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

// Formateo de texto truncado
export const truncateText = (text, maxLength = 50) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// Formateo de estado de pago
export const formatEstadoPago = (estado) => {
  const estados = {
    'pendiente': 'Pendiente',
    'al_dia': 'Al día',
    'vencido': 'Vencido',
    'parcial': 'Parcial'
  }
  return estados[estado] || estado
}

// Formateo de tipo de propiedad
export const formatTipoPropiedad = (tipo) => {
  const tipos = {
    'App\\Trastero': 'Trastero',
    'App\\Piso': 'Piso'
  }
  return tipos[tipo] || tipo
}

// Formateo de estado de disponibilidad
export const formatDisponibilidad = (disponible) => {
  return disponible ? 'Disponible' : 'Ocupado'
}

// Formateo específico para formularios

// Formatear teléfono español
export const formatPhone = (phone) => {
  if (!phone) return ''

  const cleaned = phone.replace(/\D/g, '')

  if (cleaned.length === 9) {
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3')
  }

  if (cleaned.length === 11 && cleaned.startsWith('34')) {
    const number = cleaned.substring(2)
    return `+34 ${number.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3')}`
  }

  return phone
}

// Formatear DNI/NIE
export const formatDNI = (dni) => {
  if (!dni) return ''

  const cleaned = dni.toUpperCase().replace(/\s/g, '')

  if (/^\d{8}[A-Z]$/.test(cleaned)) {
    return cleaned.replace(/(\d{8})([A-Z])/, '$1-$2')
  }

  if (/^[XYZ]\d{7}[A-Z]$/.test(cleaned)) {
    return cleaned.replace(/([XYZ])(\d{7})([A-Z])/, '$1$2-$3')
  }

  return dni
}

// Formatear dirección completa
export const formatAddress = (address) => {
  if (typeof address === 'string') return address
  if (!address) return ''

  const parts = []

  if (address.direccion) parts.push(address.direccion)
  if (address.piso) parts.push(address.piso)
  if (address.codigo_postal) parts.push(address.codigo_postal)
  if (address.ciudad) parts.push(address.ciudad)
  if (address.provincia && address.provincia !== address.ciudad) {
    parts.push(address.provincia)
  }

  return parts.join(', ')
}

// Formatear superficie
export const formatSurface = (surface, unit = 'm²') => {
  if (!surface || isNaN(surface)) return ''
  return `${formatNumber(surface, 0)} ${unit}`
}

// Formatear habitaciones/baños
export const formatRooms = (count, type = 'habitaciones') => {
  if (!count || isNaN(count)) return ''

  const number = parseFloat(count)

  if (type === 'banos' && number % 1 === 0.5) {
    const whole = Math.floor(number)
    return `${whole} ${whole === 1 ? 'baño' : 'baños'} + aseo`
  }

  const singular = type === 'habitaciones' ? 'habitación' : 'baño'
  const plural = type === 'habitaciones' ? 'habitaciones' : 'baños'

  return `${number} ${number === 1 ? singular : plural}`
}

// Formatear método de pago
export const formatPaymentMethod = (method) => {
  const methods = {
    'efectivo': 'Efectivo',
    'transferencia': 'Transferencia bancaria',
    'tarjeta': 'Tarjeta de crédito/débito',
    'cheque': 'Cheque',
    'bizum': 'Bizum',
    'paypal': 'PayPal',
    'otro': 'Otro método'
  }

  return methods[method] || capitalize(method)
}

// Formatear tipo de documento
export const formatDocumentType = (type) => {
  const types = {
    'contrato': 'Contrato de alquiler',
    'factura': 'Factura',
    'recibo': 'Recibo de pago',
    'identificacion': 'Documento de identidad',
    'nomina': 'Nómina',
    'aval': 'Aval bancario',
    'seguro': 'Póliza de seguro',
    'inventario': 'Inventario',
    'incidencia': 'Reporte de incidencia',
    'otro': 'Otro documento'
  }

  return types[type] || capitalize(type)
}

// Formatear duración de contrato
export const formatContractDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return ''

  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end - start)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  const diffMonths = Math.round(diffDays / 30)

  if (diffMonths < 1) {
    return `${diffDays} ${diffDays === 1 ? 'día' : 'días'}`
  } else if (diffMonths < 12) {
    return `${diffMonths} ${diffMonths === 1 ? 'mes' : 'meses'}`
  } else {
    const years = Math.floor(diffMonths / 12)
    const remainingMonths = diffMonths % 12

    if (remainingMonths === 0) {
      return `${years} ${years === 1 ? 'año' : 'años'}`
    } else {
      return `${years} ${years === 1 ? 'año' : 'años'} y ${remainingMonths} ${remainingMonths === 1 ? 'mes' : 'meses'}`
    }
  }
}

// Formatear estado con badge CSS
export const formatEstadoBadge = (estado) => {
  const estados = {
    'activo': { class: 'badge bg-success', text: 'Activo' },
    'inactivo': { class: 'badge bg-secondary', text: 'Inactivo' },
    'pendiente': { class: 'badge bg-warning text-dark', text: 'Pendiente' },
    'vencido': { class: 'badge bg-danger', text: 'Vencido' },
    'disponible': { class: 'badge bg-success', text: 'Disponible' },
    'ocupado': { class: 'badge bg-danger', text: 'Ocupado' },
    'mantenimiento': { class: 'badge bg-warning text-dark', text: 'Mantenimiento' },
    'reservado': { class: 'badge bg-info', text: 'Reservado' },
    'confirmado': { class: 'badge bg-success', text: 'Confirmado' },
    'rechazado': { class: 'badge bg-danger', text: 'Rechazado' },
    'borrador': { class: 'badge bg-secondary', text: 'Borrador' }
  }

  return estados[estado] || { class: 'badge bg-secondary', text: capitalize(estado) }
}

// Limpiar y formatear entrada de precio
export const cleanPriceInput = (input) => {
  if (!input) return null

  // Remover caracteres no numéricos excepto comas y puntos
  let cleaned = input.toString().replace(/[^\d,.-]/g, '')

  // Reemplazar comas por puntos
  cleaned = cleaned.replace(',', '.')

  const parsed = parseFloat(cleaned)
  return isNaN(parsed) ? null : parsed
}

// Validar y formatear código postal
export const formatPostalCode = (code) => {
  if (!code) return ''

  const cleaned = code.replace(/\D/g, '')

  if (cleaned.length === 5) {
    return cleaned
  }

  return code
}

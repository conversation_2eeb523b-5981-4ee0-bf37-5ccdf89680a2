import { defineStore } from 'pinia'
import { clientesService } from '@/services/clientes'

export const useClientesStore = defineStore('clientes', {
  state: () => ({
    clientes: [],
    cliente: null,
    isLoading: false,
    error: null,
    filters: {
      search: '',
      estado: '',
      sortBy: 'nombre',
      sortOrder: 'asc'
    },
    pagination: {
      currentPage: 1,
      itemsPerPage: 10,
      total: 0
    },
    cache: {
      lastFetch: null,
      ttl: 5 * 60 * 1000 // 5 minutos
    }
  }),

  getters: {
    // Clientes filtrados
    filteredClientes: (state) => {
      let filtered = [...state.clientes]
      
      // Filtro por búsqueda
      if (state.filters.search) {
        const term = state.filters.search.toLowerCase()
        filtered = filtered.filter(cliente => 
          cliente.nombre.toLowerCase().includes(term) ||
          cliente.apellidos.toLowerCase().includes(term) ||
          cliente.dni.toLowerCase().includes(term) ||
          cliente.email.toLowerCase().includes(term) ||
          cliente.telefono.toLowerCase().includes(term)
        )
      }
      
      // Filtro por estado
      if (state.filters.estado) {
        filtered = filtered.filter(cliente => cliente.estado === state.filters.estado)
      }
      
      // Ordenamiento
      filtered.sort((a, b) => {
        let aValue = a[state.filters.sortBy] || ''
        let bValue = b[state.filters.sortBy] || ''
        
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase()
          bValue = bValue.toLowerCase()
        }
        
        if (state.filters.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
      
      return filtered
    },

    // Estadísticas
    totalClientes: (state) => state.clientes.length,
    clientesActivos: (state) => state.clientes.filter(c => c.estado === 'activo').length,
    clientesPosibles: (state) => state.clientes.filter(c => c.estado === 'posible').length,
    clientesConfirmados: (state) => state.clientes.filter(c => c.estado === 'confirmado').length,
    clientesInactivos: (state) => state.clientes.filter(c => c.estado === 'inactivo').length,

    // Paginación
    totalPages: (state) => Math.ceil(state.filteredClientes?.length / state.pagination.itemsPerPage) || 0,
    paginatedClientes() {
      const start = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage
      const end = start + this.pagination.itemsPerPage
      return this.filteredClientes.slice(start, end)
    },

    // Cache
    isCacheValid: (state) => {
      if (!state.cache.lastFetch) return false
      return Date.now() - state.cache.lastFetch < state.cache.ttl
    }
  },

  actions: {
    // Cargar todos los clientes
    async fetchClientes(force = false) {
      // Si el cache es válido y no se fuerza la recarga, no hacer nada
      if (!force && this.isCacheValid && this.clientes.length > 0) {
        return this.clientes
      }

      this.isLoading = true
      this.error = null

      try {
        const response = await clientesService.getAll()
        this.clientes = Array.isArray(response.data.data) ? response.data.data : 
                       Array.isArray(response.data) ? response.data : []
        this.cache.lastFetch = Date.now()
        return this.clientes
      } catch (error) {
        this.error = error.response?.data?.message || 'Error al cargar clientes'
        console.error('Error al cargar clientes:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Cargar cliente por ID
    async fetchCliente(id, force = false) {
      // Buscar en cache local primero
      if (!force) {
        const cachedCliente = this.clientes.find(c => c.id == id)
        if (cachedCliente) {
          this.cliente = cachedCliente
          return cachedCliente
        }
      }

      this.isLoading = true
      this.error = null

      try {
        const response = await clientesService.getById(id)
        this.cliente = response.data
        
        // Actualizar en la lista si existe
        const index = this.clientes.findIndex(c => c.id == id)
        if (index !== -1) {
          this.clientes[index] = response.data
        }
        
        return this.cliente
      } catch (error) {
        this.error = error.response?.data?.message || 'Error al cargar cliente'
        console.error('Error al cargar cliente:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Crear cliente
    async createCliente(clienteData) {
      this.isLoading = true
      this.error = null

      try {
        const response = await clientesService.create(clienteData)
        const nuevoCliente = response.data
        
        // Agregar a la lista
        this.clientes.unshift(nuevoCliente)
        
        return nuevoCliente
      } catch (error) {
        this.error = error.response?.data?.message || 'Error al crear cliente'
        console.error('Error al crear cliente:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Actualizar cliente
    async updateCliente(id, clienteData) {
      this.isLoading = true
      this.error = null

      try {
        const response = await clientesService.update(id, clienteData)
        const clienteActualizado = response.data
        
        // Actualizar en la lista
        const index = this.clientes.findIndex(c => c.id == id)
        if (index !== -1) {
          this.clientes[index] = clienteActualizado
        }
        
        // Actualizar cliente actual si es el mismo
        if (this.cliente?.id == id) {
          this.cliente = clienteActualizado
        }
        
        return clienteActualizado
      } catch (error) {
        this.error = error.response?.data?.message || 'Error al actualizar cliente'
        console.error('Error al actualizar cliente:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Eliminar cliente
    async deleteCliente(id) {
      this.isLoading = true
      this.error = null

      try {
        await clientesService.delete(id)
        
        // Remover de la lista
        this.clientes = this.clientes.filter(c => c.id != id)
        
        // Limpiar cliente actual si es el mismo
        if (this.cliente?.id == id) {
          this.cliente = null
        }
        
        return true
      } catch (error) {
        this.error = error.response?.data?.message || 'Error al eliminar cliente'
        console.error('Error al eliminar cliente:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Filtros y búsqueda
    setSearchFilter(search) {
      this.filters.search = search
      this.pagination.currentPage = 1
    },

    setEstadoFilter(estado) {
      this.filters.estado = estado
      this.pagination.currentPage = 1
    },

    setSorting(sortBy, sortOrder = 'asc') {
      this.filters.sortBy = sortBy
      this.filters.sortOrder = sortOrder
      this.pagination.currentPage = 1
    },

    clearFilters() {
      this.filters.search = ''
      this.filters.estado = ''
      this.filters.sortBy = 'nombre'
      this.filters.sortOrder = 'asc'
      this.pagination.currentPage = 1
    },

    // Paginación
    setPage(page) {
      this.pagination.currentPage = page
    },

    setItemsPerPage(itemsPerPage) {
      this.pagination.itemsPerPage = itemsPerPage
      this.pagination.currentPage = 1
    },

    // Limpiar estado
    clearCliente() {
      this.cliente = null
    },

    clearError() {
      this.error = null
    },

    // Invalidar cache
    invalidateCache() {
      this.cache.lastFetch = null
    }
  }
})

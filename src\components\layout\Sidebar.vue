<!-- Parecque que no se usa -->
<template>
  <div class="sidebar bg-light border-end">
    <div class="sidebar-sticky pt-3">
      <ul class="nav flex-column">
        <li class="nav-item">
          <router-link to="/" class="nav-link" exact-active-class="active">
            <i class="fas fa-tachometer-alt me-2"></i>
            Dashboard
          </router-link>
        </li>
        


        <li class="nav-item">
          <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted">
            <span>Acciones Rápidas</span>
          </h6>
        </li>
        <li class="nav-item">
          <router-link to="/trasteros/nuevo" class="nav-link quick-action" active-class="active">
            <i class="fas fa-plus-circle me-2 text-success"></i>
            Nuevo Trastero
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/pisos/nuevo" class="nav-link quick-action" active-class="active">
            <i class="fas fa-plus-circle me-2 text-success"></i>
            Nuevo Piso
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/clientes/nuevo" class="nav-link quick-action" active-class="active">
            <i class="fas fa-user-plus me-2 text-primary"></i>
            Nuevo Cliente
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/alquileres/nuevo" class="nav-link quick-action" active-class="active">
            <i class="fas fa-file-plus me-2 text-info"></i>
            Nuevo Alquiler
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/documentos/nuevo" class="nav-link quick-action" active-class="active">
            <i class="fas fa-upload me-2 text-warning"></i>
            Subir Documento
          </router-link>
        </li>


      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sidebar'
}
</script>

<style scoped>
.sidebar {
  width: 280px;
  min-height: calc(100vh - 56px);
  position: sticky;
  top: 56px;
  height: calc(100vh - 56px);
  z-index: 100;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  border-right: 1px solid #dee2e6;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
  position: sticky;
  top: 0;
  height: calc(100vh - 56px);
  padding: 1rem 0;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #495057;
  padding: 0.75rem 1.5rem;
  margin: 0.25rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.sidebar .nav-link:hover {
  color: #007bff;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #007bff;
  transform: translateX(5px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.sidebar .nav-link.active {
  color: #fff;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-color: #007bff;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.sidebar .nav-link.quick-action {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px dashed #6c757d;
}

.sidebar .nav-link.quick-action:hover {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  border-color: #28a745;
  color: #155724;
}

.sidebar-heading {
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0.5px;
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.sidebar .nav-link i {
  width: 1.2rem;
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: relative;
    height: auto;
    min-height: auto;
  }

  .sidebar-sticky {
    height: auto;
    padding: 0.5rem 0;
  }

  .sidebar .nav-link {
    margin: 0.125rem 0.5rem;
    padding: 0.5rem 1rem;
  }
}
</style>
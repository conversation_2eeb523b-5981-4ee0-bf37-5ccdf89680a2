<template>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
    <div class="container-fluid">
      <!-- Brand -->
      <router-link class="navbar-brand fw-bold" to="/">
        <i class="fas fa-warehouse me-2"></i>
        BarnaTrasteros
      </router-link>

      <!-- Mobile toggle -->
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarNav"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <router-link class="nav-link" to="/" exact-active-class="active">
              <i class="fas fa-tachometer-alt me-1"></i>
              Dashboard
            </router-link>
          </li>
          
          <li class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-building me-1"></i>
              Propiedades
            </a>
            <ul class="dropdown-menu">
              <li>
                <router-link class="dropdown-item" to="/trasteros">
                  <i class="fas fa-warehouse me-2"></i>
                  Trasteros
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/pisos">
                  <i class="fas fa-home me-2"></i>
                  Pisos
                </router-link>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <router-link class="dropdown-item" to="/trasteros/nuevo">
                  <i class="fas fa-plus me-2 text-success"></i>
                  Nuevo Trastero
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/pisos/nuevo">
                  <i class="fas fa-plus me-2 text-success"></i>
                  Nuevo Piso
                </router-link>
              </li>
            </ul>
          </li>

          <li class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-users me-1"></i>
              Clientes
            </a>
            <ul class="dropdown-menu">
              <li>
                <router-link class="dropdown-item" to="/clientes">
                  <i class="fas fa-list me-2"></i>
                  Ver Clientes
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/clientes/nuevo">
                  <i class="fas fa-user-plus me-2 text-success"></i>
                  Nuevo Cliente
                </router-link>
              </li>
            </ul>
          </li>

          <li class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-file-contract me-1"></i>
              Alquileres
            </a>
            <ul class="dropdown-menu">
              <li>
                <router-link class="dropdown-item" to="/alquileres">
                  <i class="fas fa-list me-2"></i>
                  Ver Alquileres
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/alquileres/nuevo">
                  <i class="fas fa-file-plus me-2 text-success"></i>
                  Nuevo Alquiler
                </router-link>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="#" @click="showPagosRapidos">
                  <i class="fas fa-money-bill-wave me-2 text-info"></i>
                  Registrar Pago
                </a>
              </li>
            </ul>
          </li>

          <li class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-cog me-1"></i>
              Gestión
            </a>
            <ul class="dropdown-menu">
              <li>
                <router-link class="dropdown-item" to="/documentos">
                  <i class="fas fa-folder-open me-2"></i>
                  Documentos
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/documentos/nuevo">
                  <i class="fas fa-upload me-2 text-success"></i>
                  Subir Documento
                </router-link>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <router-link class="dropdown-item" to="/gastos">
                  <i class="fas fa-receipt me-2"></i>
                  Gastos
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/reportes">
                  <i class="fas fa-chart-bar me-2"></i>
                  Reportes
                </router-link>
              </li>
            </ul>
          </li>

          <!-- Menú de Administración (solo para admins) -->
          <li v-if="userStore.user?.rol === 'admin'" class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-users-cog me-1"></i>
              Administración
            </a>
            <ul class="dropdown-menu">
              <li>
                <router-link class="dropdown-item" to="/admin/usuarios">
                  <i class="fas fa-users me-2"></i>
                  Usuarios
                </router-link>
              </li>
              <li>
                <router-link class="dropdown-item" to="/admin/usuarios/nuevo">
                  <i class="fas fa-user-plus me-2 text-success"></i>
                  Nuevo Usuario
                </router-link>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <router-link class="dropdown-item" to="/admin/configuracion">
                  <i class="fas fa-cog me-2"></i>
                  Configuración
                </router-link>
              </li>
              <li>
                <a class="dropdown-item" href="#" @click="showBackupOptions">
                  <i class="fas fa-database me-2 text-warning"></i>
                  Backup & Restore
                </a>
              </li>
            </ul>
          </li>
        </ul>

        <!-- User menu -->
        <ul class="navbar-nav">
          <!-- Notifications -->
          <li class="nav-item dropdown">
            <a
              class="nav-link position-relative"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-bell"></i>
              <span
                v-if="notificationCount > 0"
                class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
              >
                {{ notificationCount }}
              </span>
            </a>
            <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
              <li class="dropdown-header">
                Notificaciones ({{ notificationCount }})
              </li>
              <li><hr class="dropdown-divider"></li>
              <li v-if="notifications.length === 0" class="dropdown-item-text text-muted">
                No hay notificaciones
              </li>
              <li v-for="notification in notifications.slice(0, 5)" :key="notification.id">
                <a class="dropdown-item" href="#">
                  <div class="d-flex">
                    <div class="flex-shrink-0">
                      <i :class="getNotificationIcon(notification.type)" class="text-primary"></i>
                    </div>
                    <div class="flex-grow-1 ms-2">
                      <div class="fw-bold">{{ notification.title }}</div>
                      <div class="small text-muted">{{ notification.message }}</div>
                      <div class="small text-muted">{{ formatTime(notification.timestamp) }}</div>
                    </div>
                  </div>
                </a>
              </li>
              <li v-if="notifications.length > 5"><hr class="dropdown-divider"></li>
              <li v-if="notifications.length > 5">
                <a class="dropdown-item text-center" href="#">
                  Ver todas las notificaciones
                </a>
              </li>
            </ul>
          </li>

          <!-- User menu -->
          <li class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle d-flex align-items-center"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <div class="user-avatar me-2">
                <i class="fas fa-user-circle fa-lg"></i>
              </div>
              <span class="d-none d-md-inline">{{ user?.name || 'Usuario' }}</span>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li class="dropdown-header">
                {{ user?.email }}
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="#" @click="showProfile">
                  <i class="fas fa-user me-2"></i>
                  Mi Perfil
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="#" @click="showSettings">
                  <i class="fas fa-cog me-2"></i>
                  Configuración
                </a>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item text-danger" href="#" @click="handleLogout">
                  <i class="fas fa-sign-out-alt me-2"></i>
                  Cerrar Sesión
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</template>

<script>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

export default {
  name: 'NavBar',
  setup() {
    const authStore = useAuthStore()
    const appStore = useAppStore()
    const userStore = authStore // Alias para compatibilidad

    const user = computed(() => authStore.user)
    const notifications = computed(() => appStore.notifications)
    const notificationCount = computed(() => notifications.value.length)

    const handleLogout = () => {
      if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
        authStore.logout()
      }
    }

    const showProfile = () => {
      // TODO: Implementar modal de perfil
      console.log('Mostrar perfil')
    }

    const showSettings = () => {
      // TODO: Implementar configuración
      console.log('Mostrar configuración')
    }

    const getNotificationIcon = (type) => {
      const icons = {
        'payment': 'fas fa-euro-sign',
        'document': 'fas fa-file',
        'expense': 'fas fa-receipt',
        'client': 'fas fa-user',
        'property': 'fas fa-building',
        'system': 'fas fa-cog'
      }
      return icons[type] || 'fas fa-info-circle'
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const now = new Date()
      const time = new Date(timestamp)
      const diff = now - time

      if (diff < 60000) return 'Hace un momento'
      if (diff < 3600000) return `Hace ${Math.floor(diff / 60000)} min`
      if (diff < 86400000) return `Hace ${Math.floor(diff / 3600000)} h`
      return time.toLocaleDateString('es-ES')
    }

    const showPagosRapidos = () => {
      // TODO: Implementar modal de pagos rápidos
      console.log('Mostrar modal de pagos rápidos')
    }

    const showBackupOptions = () => {
      // TODO: Implementar opciones de backup
      console.log('Mostrar opciones de backup')
    }

    const showNotifications = () => {
      // TODO: Implementar modal de notificaciones
      console.log('Mostrar todas las notificaciones')
    }

    const showHelp = () => {
      // TODO: Implementar sistema de ayuda
      console.log('Mostrar ayuda')
    }

    const getUserInitials = () => {
      if (!user.value) return 'U'
      const nombre = user.value.nombre || user.value.name || ''
      const apellidos = user.value.apellidos || ''
      const n = nombre.charAt(0).toUpperCase()
      const a = apellidos.charAt(0).toUpperCase()
      return n + a || 'U'
    }

    const getRolText = (rol) => {
      const roles = {
        'admin': 'Administrador',
        'manager': 'Gestor',
        'employee': 'Empleado',
        'viewer': 'Solo lectura'
      }
      return roles[rol] || 'Usuario'
    }

    return {
      user,
      userStore,
      notifications,
      notificationCount,
      handleLogout,
      showProfile,
      showSettings,
      getNotificationIcon,
      formatTime,
      showPagosRapidos,
      showBackupOptions,
      showNotifications,
      showHelp,
      getUserInitials,
      getRolText
    }
  }
}
</script>

<style scoped>
.navbar {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  z-index: 1030;
}

.navbar-brand {
  font-size: 1.5rem;
}

.nav-link {
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
}

.notification-dropdown {
  min-width: 300px;
  max-height: 400px;
  overflow-y: auto;
}

.user-avatar {
  color: rgba(255, 255, 255, 0.8);
}

.dropdown-item {
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  transform: translateX(3px);
}

.dropdown-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

.avatar-placeholder {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.text-success { color: #28a745 !important; }
.text-info { color: #17a2b8 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }

@media (max-width: 991.98px) {
  .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
  }
  
  .dropdown-menu {
    border: none;
    box-shadow: none;
    background-color: rgba(255, 255, 255, 0.95);
  }
}
</style>

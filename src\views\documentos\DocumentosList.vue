<template>
  <div class="documentos-list">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Gestión de Documentos</h1>
              <p class="text-muted mb-0">Administra todos los documentos del sistema</p>
            </div>
            <router-link to="/documentos/nuevo" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>Subir Documento
            </router-link>
          </div>
        </div>
      </div>

      <!-- Estadísticas -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card border-0 bg-primary text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Total Documentos</h6>
                  <h4 class="mb-0">{{ totalDocumentos }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-file-alt fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-warning text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Pendientes</h6>
                  <h4 class="mb-0">{{ documentosPendientes }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-clock fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Procesados</h6>
                  <h4 class="mb-0">{{ documentosProcesados }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-info text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Archivados</h6>
                  <h4 class="mb-0">{{ documentosArchivados }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-archive fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtros -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <!-- Búsqueda -->
                <div class="col-md-4 mb-3">
                  <label class="form-label">Buscar Documento</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      v-model="searchTerm"
                      type="text"
                      class="form-control"
                      placeholder="Título, descripción, tipo..."
                    >
                    <button
                      v-if="searchTerm"
                      @click="searchTerm = ''"
                      class="btn btn-outline-secondary"
                      type="button"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>

                <!-- Filtro por tipo -->
                <div class="col-md-2 mb-3">
                  <label class="form-label">Tipo</label>
                  <select v-model="filterTipo" class="form-select">
                    <option value="">Todos</option>
                    <option value="contrato">Contrato</option>
                    <option value="factura">Factura</option>
                    <option value="recibo">Recibo</option>
                    <option value="identificacion">Identificación</option>
                    <option value="nomina">Nómina</option>
                    <option value="aval">Aval</option>
                    <option value="seguro">Seguro</option>
                    <option value="inventario">Inventario</option>
                    <option value="incidencia">Incidencia</option>
                    <option value="otro">Otro</option>
                  </select>
                </div>

                <!-- Filtro por estado -->
                <div class="col-md-2 mb-3">
                  <label class="form-label">Estado</label>
                  <select v-model="filterEstado" class="form-select">
                    <option value="">Todos</option>
                    <option value="pendiente">Pendiente</option>
                    <option value="procesado">Procesado</option>
                    <option value="archivado">Archivado</option>
                    <option value="rechazado">Rechazado</option>
                  </select>
                </div>

                <!-- Filtro por fecha -->
                <div class="col-md-2 mb-3">
                  <label class="form-label">Desde</label>
                  <input
                    v-model="filterFechaDesde"
                    type="date"
                    class="form-control"
                  >
                </div>

                <div class="col-md-2 mb-3">
                  <label class="form-label">Hasta</label>
                  <input
                    v-model="filterFechaHasta"
                    type="date"
                    class="form-control"
                  >
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <button
                        @click="clearFilters"
                        class="btn btn-outline-secondary btn-sm"
                        :disabled="!hasFilters"
                      >
                        <i class="fas fa-times me-1"></i>Limpiar filtros
                      </button>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                      <span class="text-muted small">Vista:</span>
                      <div class="btn-group" role="group">
                        <button
                          @click="viewMode = 'table'"
                          class="btn btn-sm"
                          :class="viewMode === 'table' ? 'btn-primary' : 'btn-outline-primary'"
                        >
                          <i class="fas fa-table"></i>
                        </button>
                        <button
                          @click="viewMode = 'grid'"
                          class="btn btn-sm"
                          :class="viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary'"
                        >
                          <i class="fas fa-th"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contenido principal -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="fas fa-file-alt me-2"></i>
                Lista de Documentos
                <span class="badge bg-secondary ms-2">{{ filteredDocumentos.length }}</span>
              </h5>
              <div class="d-flex align-items-center gap-2">
                <span class="text-muted small">
                  Mostrando {{ startIndex + 1 }}-{{ Math.min(endIndex, filteredDocumentos.length) }}
                  de {{ filteredDocumentos.length }} documentos
                </span>
              </div>
            </div>

            <div class="card-body p-0">
              <!-- Loading -->
              <div v-if="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="text-muted mt-2">Cargando documentos...</p>
              </div>

              <!-- Sin resultados -->
              <div v-else-if="filteredDocumentos.length === 0" class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h5>No se encontraron documentos</h5>
                <p class="text-muted">
                  {{ hasFilters ? 'No hay documentos que coincidan con los filtros aplicados' : 'No hay documentos registrados' }}
                </p>
                <router-link v-if="!hasFilters" to="/documentos/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Subir primer documento
                </router-link>
                <button v-else @click="clearFilters" class="btn btn-outline-primary">
                  <i class="fas fa-times me-1"></i>Limpiar filtros
                </button>
              </div>

              <!-- Vista de tabla -->
              <div v-else-if="viewMode === 'table'" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Documento</th>
                      <th>Tipo</th>
                      <th>Estado</th>
                      <th>Asociado con</th>
                      <th>Fechas</th>
                      <th>Tamaño</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="documento in paginatedDocumentos" :key="documento.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="me-3">
                            <i :class="getFileIcon(documento.archivo?.extension)" class="fa-2x"></i>
                          </div>
                          <div>
                            <div class="fw-bold">{{ documento.titulo }}</div>
                            <div class="text-muted small">{{ documento.descripcion || 'Sin descripción' }}</div>
                            <div class="text-muted small">
                              <i class="fas fa-file me-1"></i>{{ documento.archivo?.nombre_original }}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="badge" :class="getTipoClass(documento.tipo)">
                          {{ getTipoText(documento.tipo) }}
                        </span>
                      </td>
                      <td>
                        <span class="badge" :class="getEstadoClass(documento.estado)">
                          {{ getEstadoText(documento.estado) }}
                        </span>
                        <div v-if="documento.es_confidencial" class="mt-1">
                          <span class="badge bg-warning text-dark">
                            <i class="fas fa-lock me-1"></i>Confidencial
                          </span>
                        </div>
                      </td>
                      <td>
                        <div v-if="documento.cliente" class="small">
                          <i class="fas fa-user me-1"></i>{{ documento.cliente.nombre }} {{ documento.cliente.apellidos }}
                        </div>
                        <div v-else-if="documento.propiedad" class="small">
                          <i class="fas fa-building me-1"></i>
                          {{ documento.tipo_propiedad === 'App\\Trastero' ? 'Trastero' : 'Piso' }}
                          {{ documento.propiedad.numero }}
                        </div>
                        <div v-else-if="documento.alquiler" class="small">
                          <i class="fas fa-file-contract me-1"></i>Contrato {{ documento.alquiler.id }}
                        </div>
                        <div v-else class="small text-muted">
                          <i class="fas fa-building me-1"></i>General
                        </div>
                      </td>
                      <td>
                        <div class="small">
                          <div v-if="documento.fecha_documento">
                            <strong>Doc:</strong> {{ formatFecha(documento.fecha_documento) }}
                          </div>
                          <div v-if="documento.fecha_vencimiento" :class="{ 'text-danger': isVencido(documento.fecha_vencimiento) }">
                            <strong>Vence:</strong> {{ formatFecha(documento.fecha_vencimiento) }}
                            <i v-if="isVencido(documento.fecha_vencimiento)" class="fas fa-exclamation-triangle ms-1"></i>
                          </div>
                          <div class="text-muted">
                            <strong>Subido:</strong> {{ formatFecha(documento.created_at) }}
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="small">{{ formatFileSize(documento.archivo?.tamano) }}</span>
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <button
                            @click="downloadDocumento(documento)"
                            class="btn btn-sm btn-outline-primary"
                            title="Descargar"
                            :disabled="isDownloading === documento.id"
                          >
                            <i class="fas fa-download" v-if="isDownloading !== documento.id"></i>
                            <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                          </button>
                          <button
                            @click="viewDocumento(documento)"
                            class="btn btn-sm btn-outline-info"
                            title="Ver detalles"
                          >
                            <i class="fas fa-eye"></i>
                          </button>
                          <router-link
                            :to="`/documentos/${documento.id}/editar`"
                            class="btn btn-sm btn-outline-warning"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            @click="confirmarEliminacion(documento)"
                            class="btn btn-sm btn-outline-danger"
                            title="Eliminar"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>

                        <!-- Acciones adicionales -->
                        <div class="mt-1">
                          <div class="btn-group" role="group" v-if="documento.estado === 'pendiente'">
                            <button
                              @click="procesarDocumento(documento)"
                              class="btn btn-sm btn-outline-success"
                              title="Marcar como procesado"
                              :disabled="isProcessing === documento.id"
                            >
                              <i class="fas fa-check" v-if="isProcessing !== documento.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                            </button>
                          </div>
                          <div class="btn-group" role="group" v-if="documento.estado === 'procesado'">
                            <button
                              @click="archivarDocumento(documento)"
                              class="btn btn-sm btn-outline-secondary"
                              title="Archivar"
                              :disabled="isArchiving === documento.id"
                            >
                              <i class="fas fa-archive" v-if="isArchiving !== documento.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                            </button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Vista de grid -->
              <div v-else class="p-3">
                <div class="row">
                  <div v-for="documento in paginatedDocumentos" :key="documento.id" class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100 documento-card">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                          <i :class="getFileIcon(documento.archivo?.extension)" class="me-2"></i>
                          <span class="badge" :class="getTipoClass(documento.tipo)">
                            {{ getTipoText(documento.tipo) }}
                          </span>
                        </div>
                        <span class="badge" :class="getEstadoClass(documento.estado)">
                          {{ getEstadoText(documento.estado) }}
                        </span>
                      </div>
                      <div class="card-body">
                        <h6 class="card-title">{{ documento.titulo }}</h6>
                        <p class="card-text text-muted small">{{ documento.descripcion || 'Sin descripción' }}</p>

                        <div class="mb-2">
                          <small class="text-muted">Archivo:</small>
                          <div class="small">{{ documento.archivo?.nombre_original }}</div>
                        </div>

                        <div class="mb-2" v-if="documento.cliente || documento.propiedad || documento.alquiler">
                          <small class="text-muted">Asociado con:</small>
                          <div class="small">
                            <span v-if="documento.cliente">
                              <i class="fas fa-user me-1"></i>{{ documento.cliente.nombre }} {{ documento.cliente.apellidos }}
                            </span>
                            <span v-else-if="documento.propiedad">
                              <i class="fas fa-building me-1"></i>
                              {{ documento.tipo_propiedad === 'App\\Trastero' ? 'Trastero' : 'Piso' }}
                              {{ documento.propiedad.numero }}
                            </span>
                            <span v-else-if="documento.alquiler">
                              <i class="fas fa-file-contract me-1"></i>Contrato {{ documento.alquiler.id }}
                            </span>
                          </div>
                        </div>

                        <div class="mb-2">
                          <small class="text-muted">Fechas:</small>
                          <div class="small">
                            <div v-if="documento.fecha_documento">
                              <strong>Doc:</strong> {{ formatFecha(documento.fecha_documento) }}
                            </div>
                            <div v-if="documento.fecha_vencimiento" :class="{ 'text-danger': isVencido(documento.fecha_vencimiento) }">
                              <strong>Vence:</strong> {{ formatFecha(documento.fecha_vencimiento) }}
                              <i v-if="isVencido(documento.fecha_vencimiento)" class="fas fa-exclamation-triangle ms-1"></i>
                            </div>
                          </div>
                        </div>

                        <div class="mb-2">
                          <small class="text-muted">Tamaño:</small>
                          <span class="small">{{ formatFileSize(documento.archivo?.tamano) }}</span>
                        </div>

                        <div v-if="documento.es_confidencial" class="mb-2">
                          <span class="badge bg-warning text-dark">
                            <i class="fas fa-lock me-1"></i>Confidencial
                          </span>
                        </div>
                      </div>
                      <div class="card-footer">
                        <div class="d-flex justify-content-between">
                          <div class="btn-group" role="group">
                            <button
                              @click="downloadDocumento(documento)"
                              class="btn btn-sm btn-outline-primary"
                              title="Descargar"
                              :disabled="isDownloading === documento.id"
                            >
                              <i class="fas fa-download" v-if="isDownloading !== documento.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                            </button>
                            <button
                              @click="viewDocumento(documento)"
                              class="btn btn-sm btn-outline-info"
                              title="Ver detalles"
                            >
                              <i class="fas fa-eye"></i>
                            </button>
                          </div>
                          <div class="btn-group" role="group">
                            <router-link
                              :to="`/documentos/${documento.id}/editar`"
                              class="btn btn-sm btn-outline-warning"
                              title="Editar"
                            >
                              <i class="fas fa-edit"></i>
                            </router-link>
                            <button
                              @click="confirmarEliminacion(documento)"
                              class="btn btn-sm btn-outline-danger"
                              title="Eliminar"
                            >
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </div>

                        <!-- Acciones adicionales -->
                        <div class="mt-2" v-if="documento.estado === 'pendiente' || documento.estado === 'procesado'">
                          <div class="d-flex gap-1">
                            <button
                              v-if="documento.estado === 'pendiente'"
                              @click="procesarDocumento(documento)"
                              class="btn btn-sm btn-outline-success flex-fill"
                              title="Marcar como procesado"
                              :disabled="isProcessing === documento.id"
                            >
                              <i class="fas fa-check" v-if="isProcessing !== documento.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                              <span class="ms-1">Procesar</span>
                            </button>
                            <button
                              v-if="documento.estado === 'procesado'"
                              @click="archivarDocumento(documento)"
                              class="btn btn-sm btn-outline-secondary flex-fill"
                              title="Archivar"
                              :disabled="isArchiving === documento.id"
                            >
                              <i class="fas fa-archive" v-if="isArchiving !== documento.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                              <span class="ms-1">Archivar</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Paginación -->
            <div v-if="totalPages > 1" class="card-footer">
              <nav aria-label="Paginación de documentos">
                <ul class="pagination justify-content-center mb-0">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="goToPage(1)" :disabled="currentPage === 1">
                      <i class="fas fa-angle-double-left"></i>
                    </button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">
                      <i class="fas fa-angle-left"></i>
                    </button>
                  </li>

                  <li
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === currentPage }"
                  >
                    <button class="page-link" @click="goToPage(page)">{{ page }}</button>
                  </li>

                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
                      <i class="fas fa-angle-right"></i>
                    </button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="goToPage(totalPages)" :disabled="currentPage === totalPages">
                      <i class="fas fa-angle-double-right"></i>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmación de eliminación -->
    <div class="modal fade" id="deleteModal" tabindex="-1" ref="deleteModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmar eliminación</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>¿Estás seguro de que deseas eliminar el documento <strong>{{ documentoAEliminar?.titulo }}</strong>?</p>
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Esta acción no se puede deshacer. El archivo se eliminará permanentemente del sistema.
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button
              type="button"
              class="btn btn-danger"
              @click="eliminarDocumento"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="spinner-border spinner-border-sm me-2"></span>
              {{ isDeleting ? 'Eliminando...' : 'Eliminar documento' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de vista de documento -->
    <div class="modal fade" id="viewModal" tabindex="-1" ref="viewModal">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Detalles del Documento</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" v-if="documentoSeleccionado">
            <div class="row">
              <div class="col-md-8">
                <h6>{{ documentoSeleccionado.titulo }}</h6>
                <p class="text-muted">{{ documentoSeleccionado.descripcion || 'Sin descripción' }}</p>

                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Tipo:</strong></div>
                  <div class="col-sm-8">
                    <span class="badge" :class="getTipoClass(documentoSeleccionado.tipo)">
                      {{ getTipoText(documentoSeleccionado.tipo) }}
                    </span>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Estado:</strong></div>
                  <div class="col-sm-8">
                    <span class="badge" :class="getEstadoClass(documentoSeleccionado.estado)">
                      {{ getEstadoText(documentoSeleccionado.estado) }}
                    </span>
                  </div>
                </div>

                <div class="row mb-3" v-if="documentoSeleccionado.fecha_documento">
                  <div class="col-sm-4"><strong>Fecha documento:</strong></div>
                  <div class="col-sm-8">{{ formatFecha(documentoSeleccionado.fecha_documento) }}</div>
                </div>

                <div class="row mb-3" v-if="documentoSeleccionado.fecha_vencimiento">
                  <div class="col-sm-4"><strong>Fecha vencimiento:</strong></div>
                  <div class="col-sm-8" :class="{ 'text-danger': isVencido(documentoSeleccionado.fecha_vencimiento) }">
                    {{ formatFecha(documentoSeleccionado.fecha_vencimiento) }}
                    <i v-if="isVencido(documentoSeleccionado.fecha_vencimiento)" class="fas fa-exclamation-triangle ms-1"></i>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Archivo:</strong></div>
                  <div class="col-sm-8">
                    <div>{{ documentoSeleccionado.archivo?.nombre_original }}</div>
                    <small class="text-muted">{{ formatFileSize(documentoSeleccionado.archivo?.tamano) }}</small>
                  </div>
                </div>

                <div class="row mb-3" v-if="documentoSeleccionado.es_confidencial">
                  <div class="col-sm-4"><strong>Confidencial:</strong></div>
                  <div class="col-sm-8">
                    <span class="badge bg-warning text-dark">
                      <i class="fas fa-lock me-1"></i>Sí
                    </span>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="text-center">
                  <i :class="getFileIcon(documentoSeleccionado.archivo?.extension)" class="fa-4x mb-3"></i>
                  <div class="d-grid gap-2">
                    <button
                      @click="downloadDocumento(documentoSeleccionado)"
                      class="btn btn-primary"
                      :disabled="isDownloading === documentoSeleccionado.id"
                    >
                      <i class="fas fa-download me-1" v-if="isDownloading !== documentoSeleccionado.id"></i>
                      <div v-else class="spinner-border spinner-border-sm me-1" role="status"></div>
                      Descargar
                    </button>
                    <router-link
                      :to="`/documentos/${documentoSeleccionado.id}/editar`"
                      class="btn btn-outline-warning"
                      data-bs-dismiss="modal"
                    >
                      <i class="fas fa-edit me-1"></i>Editar
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { documentosService } from '@/services/documentos'
import { formatFecha } from '@/utils/formatters'
import { Modal } from 'bootstrap'
import moment from 'moment'

export default {
  name: 'DocumentosList',
  setup() {
    const toast = useToast()

    // Estado reactivo
    const documentos = ref([])
    const isLoading = ref(false)
    const isDeleting = ref(false)
    const isDownloading = ref(null)
    const isProcessing = ref(null)
    const isArchiving = ref(null)
    const searchTerm = ref('')
    const filterTipo = ref('')
    const filterEstado = ref('')
    const filterFechaDesde = ref('')
    const filterFechaHasta = ref('')
    const viewMode = ref('table')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const documentoAEliminar = ref(null)
    const documentoSeleccionado = ref(null)
    const deleteModal = ref(null)
    const viewModal = ref(null)

    // Computed properties
    const filteredDocumentos = computed(() => {
      let filtered = Array.isArray(documentos.value) ? documentos.value : []

      // Filtro por búsqueda
      if (searchTerm.value) {
        const search = searchTerm.value.toLowerCase()
        filtered = filtered.filter(doc =>
          doc.titulo?.toLowerCase().includes(search) ||
          doc.descripcion?.toLowerCase().includes(search) ||
          doc.tipo?.toLowerCase().includes(search) ||
          doc.archivo?.nombre_original?.toLowerCase().includes(search)
        )
      }

      // Filtro por tipo
      if (filterTipo.value) {
        filtered = filtered.filter(doc => doc.tipo === filterTipo.value)
      }

      // Filtro por estado
      if (filterEstado.value) {
        filtered = filtered.filter(doc => doc.estado === filterEstado.value)
      }

      // Filtro por fecha
      if (filterFechaDesde.value) {
        filtered = filtered.filter(doc =>
          moment(doc.created_at).isSameOrAfter(moment(filterFechaDesde.value))
        )
      }

      if (filterFechaHasta.value) {
        filtered = filtered.filter(doc =>
          moment(doc.created_at).isSameOrBefore(moment(filterFechaHasta.value))
        )
      }

      return filtered
    })

    const totalDocumentos = computed(() => Array.isArray(documentos.value) ? documentos.value.length : 0)
    const documentosPendientes = computed(() =>
      Array.isArray(documentos.value) ? documentos.value.filter(d => d.estado === 'pendiente').length : 0
    )
    const documentosProcesados = computed(() =>
      Array.isArray(documentos.value) ? documentos.value.filter(d => d.estado === 'procesado').length : 0
    )
    const documentosArchivados = computed(() =>
      Array.isArray(documentos.value) ? documentos.value.filter(d => d.estado === 'archivado').length : 0
    )

    const hasFilters = computed(() => {
      return searchTerm.value || filterTipo.value || filterEstado.value ||
             filterFechaDesde.value || filterFechaHasta.value
    })

    const totalPages = computed(() => Math.ceil(filteredDocumentos.value.length / itemsPerPage.value))
    const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
    const endIndex = computed(() => startIndex.value + itemsPerPage.value)

    const paginatedDocumentos = computed(() => {
      return filteredDocumentos.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) pages.push(i)
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    // Métodos
    const loadDocumentos = async () => {
      isLoading.value = true
      try {
        const response = await documentosService.getAll()
        documentos.value = Array.isArray(response.data.data) ? response.data.data :
                          Array.isArray(response.data) ? response.data : []
      } catch (error) {
        console.error('Error al cargar documentos:', error)
        toast.error('Error al cargar la lista de documentos')
        documentos.value = []
      } finally {
        isLoading.value = false
      }
    }

    const clearFilters = () => {
      searchTerm.value = ''
      filterTipo.value = ''
      filterEstado.value = ''
      filterFechaDesde.value = ''
      filterFechaHasta.value = ''
      currentPage.value = 1
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const confirmarEliminacion = (documento) => {
      documentoAEliminar.value = documento
      const modal = new Modal(deleteModal.value)
      modal.show()
    }

    const eliminarDocumento = async () => {
      if (!documentoAEliminar.value) return

      isDeleting.value = true
      try {
        await documentosService.delete(documentoAEliminar.value.id)
        toast.success('Documento eliminado correctamente')
        await loadDocumentos()

        const modal = Modal.getInstance(deleteModal.value)
        modal.hide()
        documentoAEliminar.value = null
      } catch (error) {
        console.error('Error al eliminar documento:', error)
        toast.error('Error al eliminar el documento')
      } finally {
        isDeleting.value = false
      }
    }

    const downloadDocumento = async (documento) => {
      isDownloading.value = documento.id
      try {
        const response = await documentosService.download(documento.id)

        // Crear blob y descargar
        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = documento.archivo?.nombre_original || `documento_${documento.id}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        toast.success('Documento descargado correctamente')
      } catch (error) {
        console.error('Error al descargar documento:', error)
        toast.error('Error al descargar el documento')
      } finally {
        isDownloading.value = null
      }
    }

    const viewDocumento = (documento) => {
      documentoSeleccionado.value = documento
      const modal = new Modal(viewModal.value)
      modal.show()
    }

    const procesarDocumento = async (documento) => {
      isProcessing.value = documento.id
      try {
        await documentosService.procesar(documento.id)
        toast.success('Documento marcado como procesado')
        await loadDocumentos()
      } catch (error) {
        console.error('Error al procesar documento:', error)
        toast.error('Error al procesar el documento')
      } finally {
        isProcessing.value = null
      }
    }

    const archivarDocumento = async (documento) => {
      isArchiving.value = documento.id
      try {
        await documentosService.archivar(documento.id)
        toast.success('Documento archivado correctamente')
        await loadDocumentos()
      } catch (error) {
        console.error('Error al archivar documento:', error)
        toast.error('Error al archivar el documento')
      } finally {
        isArchiving.value = null
      }
    }

    const getFileIcon = (extension) => {
      if (!extension) return 'fas fa-file text-secondary'

      const ext = extension.toLowerCase()
      const iconMap = {
        pdf: 'fas fa-file-pdf text-danger',
        doc: 'fas fa-file-word text-primary',
        docx: 'fas fa-file-word text-primary',
        xls: 'fas fa-file-excel text-success',
        xlsx: 'fas fa-file-excel text-success',
        jpg: 'fas fa-file-image text-info',
        jpeg: 'fas fa-file-image text-info',
        png: 'fas fa-file-image text-info',
        gif: 'fas fa-file-image text-info',
        txt: 'fas fa-file-alt text-secondary',
        zip: 'fas fa-file-archive text-warning',
        rar: 'fas fa-file-archive text-warning'
      }

      return iconMap[ext] || 'fas fa-file text-secondary'
    }

    const getTipoClass = (tipo) => {
      const classMap = {
        contrato: 'bg-primary',
        factura: 'bg-success',
        recibo: 'bg-info',
        identificacion: 'bg-warning text-dark',
        nomina: 'bg-secondary',
        aval: 'bg-dark',
        seguro: 'bg-danger',
        inventario: 'bg-light text-dark',
        incidencia: 'bg-warning text-dark',
        otro: 'bg-secondary'
      }
      return classMap[tipo] || 'bg-secondary'
    }

    const getTipoText = (tipo) => {
      const textMap = {
        contrato: 'Contrato',
        factura: 'Factura',
        recibo: 'Recibo',
        identificacion: 'Identificación',
        nomina: 'Nómina',
        aval: 'Aval',
        seguro: 'Seguro',
        inventario: 'Inventario',
        incidencia: 'Incidencia',
        otro: 'Otro'
      }
      return textMap[tipo] || tipo
    }

    const getEstadoClass = (estado) => {
      const classMap = {
        pendiente: 'bg-warning text-dark',
        procesado: 'bg-success',
        archivado: 'bg-secondary',
        rechazado: 'bg-danger',
        borrador: 'bg-light text-dark'
      }
      return classMap[estado] || 'bg-secondary'
    }

    const getEstadoText = (estado) => {
      const textMap = {
        pendiente: 'Pendiente',
        procesado: 'Procesado',
        archivado: 'Archivado',
        rechazado: 'Rechazado',
        borrador: 'Borrador'
      }
      return textMap[estado] || estado
    }

    const formatFileSize = (bytes) => {
      if (!bytes || bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const isVencido = (fecha) => {
      if (!fecha) return false
      return moment(fecha).isBefore(moment(), 'day')
    }

    // Lifecycle
    onMounted(() => {
      loadDocumentos()
    })

    return {
      documentos,
      isLoading,
      isDeleting,
      isDownloading,
      isProcessing,
      isArchiving,
      searchTerm,
      filterTipo,
      filterEstado,
      filterFechaDesde,
      filterFechaHasta,
      viewMode,
      currentPage,
      itemsPerPage,
      documentoAEliminar,
      documentoSeleccionado,
      deleteModal,
      viewModal,
      filteredDocumentos,
      totalDocumentos,
      documentosPendientes,
      documentosProcesados,
      documentosArchivados,
      hasFilters,
      totalPages,
      startIndex,
      endIndex,
      paginatedDocumentos,
      visiblePages,
      loadDocumentos,
      clearFilters,
      goToPage,
      confirmarEliminacion,
      eliminarDocumento,
      downloadDocumento,
      viewDocumento,
      procesarDocumento,
      archivarDocumento,
      getFileIcon,
      getTipoClass,
      getTipoText,
      getEstadoClass,
      getEstadoText,
      formatFileSize,
      formatFecha,
      isVencido
    }
  }
}
</script>

<style scoped>
.documentos-list .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.documentos-list .card-title {
  color: #495057;
  font-weight: 600;
}

.documento-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e3e6f0;
}

.documento-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.documento-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
}

.documento-card .card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
}

.table td {
  vertical-align: middle;
  border-color: #e3e6f0;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

.btn-group .btn {
  border-radius: 0.375rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.pagination .page-link {
  border-radius: 0.375rem;
  margin: 0 0.125rem;
  border-color: #dee2e6;
  color: #495057;
}

.pagination .page-item.active .page-link {
  background-color: #4e73df;
  border-color: #4e73df;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .documentos-list .card-body {
    padding: 1rem;
  }

  .documentos-list .btn-group {
    flex-direction: column;
  }

  .documentos-list .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  .documentos-list .table-responsive {
    font-size: 0.875rem;
  }

  .documento-card .card-footer .d-flex {
    flex-direction: column;
    gap: 0.5rem;
  }

  .documento-card .card-footer .btn-group {
    width: 100%;
  }

  .documento-card .card-footer .btn {
    flex: 1;
  }
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.documento-card {
  animation: fadeIn 0.3s ease;
}

.table tbody tr {
  animation: fadeIn 0.3s ease;
}
</style>

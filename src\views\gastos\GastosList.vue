<template>
  <div class="gastos-list">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Gestión de Gastos</h1>
              <p class="text-muted mb-0">Administra todos los gastos del edificio</p>
            </div>
            <router-link to="/gastos/nuevo" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>Nuevo Gasto
            </router-link>
          </div>
        </div>
      </div>

      <!-- Estadísticas -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card border-0 bg-primary text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Total Gastos</h6>
                  <h4 class="mb-0">{{ totalGastos }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-receipt fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-warning text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Pendientes</h6>
                  <h4 class="mb-0">{{ gastosPendientes }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-clock fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Pagados</h6>
                  <h4 class="mb-0">{{ gastosPagados }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-danger text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Vencidos</h6>
                  <h4 class="mb-0">{{ gastosVencidos }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Resumen financiero -->
      <div class="row mb-4">
        <div class="col-md-4">
          <div class="card border-0 bg-info text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Total Importe</h6>
                  <h4 class="mb-0">{{ formatPrice(totalImporte) }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-euro-sign fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-0 bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Importe Pagado</h6>
                  <h4 class="mb-0">{{ formatPrice(importePagado) }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-0 bg-warning text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-0">Importe Pendiente</h6>
                  <h4 class="mb-0">{{ formatPrice(importePendiente) }}</h4>
                </div>
                <div class="ms-3">
                  <i class="fas fa-hourglass-half fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtros -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <!-- Búsqueda -->
                <div class="col-md-3 mb-3">
                  <label class="form-label">Buscar Gasto</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      v-model="searchTerm"
                      type="text"
                      class="form-control"
                      placeholder="Concepto, descripción..."
                    >
                    <button
                      v-if="searchTerm"
                      @click="searchTerm = ''"
                      class="btn btn-outline-secondary"
                      type="button"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>

                <!-- Filtro por categoría -->
                <div class="col-md-2 mb-3">
                  <label class="form-label">Categoría</label>
                  <select v-model="filterCategoria" class="form-select">
                    <option value="">Todas</option>
                    <option value="mantenimiento">Mantenimiento</option>
                    <option value="limpieza">Limpieza</option>
                    <option value="seguridad">Seguridad</option>
                    <option value="suministros">Suministros</option>
                    <option value="seguros">Seguros</option>
                    <option value="administracion">Administración</option>
                    <option value="reparaciones">Reparaciones</option>
                    <option value="otros">Otros</option>
                  </select>
                </div>

                <!-- Filtro por estado -->
                <div class="col-md-2 mb-3">
                  <label class="form-label">Estado</label>
                  <select v-model="filterEstado" class="form-select">
                    <option value="">Todos</option>
                    <option value="pendiente">Pendiente</option>
                    <option value="pagado">Pagado</option>
                    <option value="vencido">Vencido</option>
                  </select>
                </div>

                <!-- Filtro por fecha -->
                <div class="col-md-2 mb-3">
                  <label class="form-label">Desde</label>
                  <input
                    v-model="filterFechaDesde"
                    type="date"
                    class="form-control"
                  >
                </div>

                <div class="col-md-2 mb-3">
                  <label class="form-label">Hasta</label>
                  <input
                    v-model="filterFechaHasta"
                    type="date"
                    class="form-control"
                  >
                </div>

                <!-- Filtro por importe -->
                <div class="col-md-1 mb-3">
                  <label class="form-label">Importe mín.</label>
                  <input
                    v-model="filterImporteMin"
                    type="number"
                    step="0.01"
                    class="form-control"
                    placeholder="0"
                  >
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <button
                        @click="clearFilters"
                        class="btn btn-outline-secondary btn-sm"
                        :disabled="!hasFilters"
                      >
                        <i class="fas fa-times me-1"></i>Limpiar filtros
                      </button>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                      <span class="text-muted small">Vista:</span>
                      <div class="btn-group" role="group">
                        <button
                          @click="viewMode = 'table'"
                          class="btn btn-sm"
                          :class="viewMode === 'table' ? 'btn-primary' : 'btn-outline-primary'"
                        >
                          <i class="fas fa-table"></i>
                        </button>
                        <button
                          @click="viewMode = 'grid'"
                          class="btn btn-sm"
                          :class="viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary'"
                        >
                          <i class="fas fa-th"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contenido principal -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="fas fa-receipt me-2"></i>
                Lista de Gastos
                <span class="badge bg-secondary ms-2">{{ filteredGastos.length }}</span>
              </h5>
              <div class="d-flex align-items-center gap-2">
                <span class="text-muted small">
                  Mostrando {{ startIndex + 1 }}-{{ Math.min(endIndex, filteredGastos.length) }}
                  de {{ filteredGastos.length }} gastos
                </span>
              </div>
            </div>

            <div class="card-body p-0">
              <!-- Loading -->
              <div v-if="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="text-muted mt-2">Cargando gastos...</p>
              </div>

              <!-- Sin resultados -->
              <div v-else-if="filteredGastos.length === 0" class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5>No se encontraron gastos</h5>
                <p class="text-muted">
                  {{ hasFilters ? 'No hay gastos que coincidan con los filtros aplicados' : 'No hay gastos registrados' }}
                </p>
                <router-link v-if="!hasFilters" to="/gastos/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Registrar primer gasto
                </router-link>
                <button v-else @click="clearFilters" class="btn btn-outline-primary">
                  <i class="fas fa-times me-1"></i>Limpiar filtros
                </button>
              </div>

              <!-- Vista de tabla -->
              <div v-else-if="viewMode === 'table'" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Concepto</th>
                      <th>Categoría</th>
                      <th>Importe</th>
                      <th>Fecha</th>
                      <th>Estado</th>
                      <th>Fecha Pago</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="gasto in paginatedGastos" :key="gasto.id">
                      <td>
                        <div>
                          <div class="fw-bold">{{ gasto.concepto }}</div>
                          <div class="text-muted small" v-if="gasto.descripcion">{{ gasto.descripcion }}</div>
                          <div class="text-muted small">
                            <i class="fas fa-hashtag me-1"></i>ID: {{ gasto.id }}
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="badge" :class="getCategoriaClass(gasto.categoria)">
                          {{ getCategoriaText(gasto.categoria) }}
                        </span>
                      </td>
                      <td>
                        <div class="fw-bold">{{ formatPrice(gasto.importe) }}</div>
                        <div class="text-muted small" v-if="gasto.fecha_vencimiento">
                          Vence: {{ formatFecha(gasto.fecha_vencimiento) }}
                        </div>
                      </td>
                      <td>
                        <div>{{ formatFecha(gasto.fecha) }}</div>
                        <div class="text-muted small">{{ formatFechaRelativa(gasto.fecha) }}</div>
                      </td>
                      <td>
                        <span class="badge" :class="getEstadoClass(gasto.estado)">
                          {{ getEstadoText(gasto.estado) }}
                        </span>
                        <div v-if="gasto.estado === 'vencido'" class="mt-1">
                          <span class="badge bg-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>Vencido
                          </span>
                        </div>
                      </td>
                      <td>
                        <div v-if="gasto.fecha_pago">
                          <div>{{ formatFecha(gasto.fecha_pago) }}</div>
                          <div class="text-muted small">{{ formatFechaRelativa(gasto.fecha_pago) }}</div>
                        </div>
                        <div v-else class="text-muted">-</div>
                      </td>
                      <td>
                        <div class="btn-group" role="group">
                          <router-link
                            :to="`/gastos/${gasto.id}/editar`"
                            class="btn btn-sm btn-outline-warning"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            @click="confirmarEliminacion(gasto)"
                            class="btn btn-sm btn-outline-danger"
                            title="Eliminar"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>

                        <!-- Acciones adicionales -->
                        <div class="mt-1">
                          <div class="btn-group" role="group" v-if="gasto.estado === 'pendiente' || gasto.estado === 'vencido'">
                            <button
                              @click="marcarComoPagado(gasto)"
                              class="btn btn-sm btn-outline-success"
                              title="Marcar como pagado"
                              :disabled="isMarkingPaid === gasto.id"
                            >
                              <i class="fas fa-check" v-if="isMarkingPaid !== gasto.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                            </button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Vista de grid -->
              <div v-else class="p-3">
                <div class="row">
                  <div v-for="gasto in paginatedGastos" :key="gasto.id" class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100 gasto-card">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="badge" :class="getCategoriaClass(gasto.categoria)">
                          {{ getCategoriaText(gasto.categoria) }}
                        </span>
                        <span class="badge" :class="getEstadoClass(gasto.estado)">
                          {{ getEstadoText(gasto.estado) }}
                        </span>
                      </div>
                      <div class="card-body">
                        <h6 class="card-title">{{ gasto.concepto }}</h6>
                        <p class="card-text text-muted small" v-if="gasto.descripcion">{{ gasto.descripcion }}</p>

                        <div class="mb-2">
                          <small class="text-muted">Importe:</small>
                          <div class="h5 text-primary mb-0">{{ formatPrice(gasto.importe) }}</div>
                        </div>

                        <div class="mb-2">
                          <small class="text-muted">Fecha:</small>
                          <div class="small">{{ formatFecha(gasto.fecha) }}</div>
                          <div class="text-muted small">{{ formatFechaRelativa(gasto.fecha) }}</div>
                        </div>

                        <div class="mb-2" v-if="gasto.fecha_vencimiento">
                          <small class="text-muted">Vencimiento:</small>
                          <div class="small" :class="{ 'text-danger': isVencido(gasto.fecha_vencimiento) }">
                            {{ formatFecha(gasto.fecha_vencimiento) }}
                            <i v-if="isVencido(gasto.fecha_vencimiento)" class="fas fa-exclamation-triangle ms-1"></i>
                          </div>
                        </div>

                        <div class="mb-2" v-if="gasto.fecha_pago">
                          <small class="text-muted">Fecha de pago:</small>
                          <div class="small">{{ formatFecha(gasto.fecha_pago) }}</div>
                        </div>
                      </div>
                      <div class="card-footer">
                        <div class="d-flex justify-content-between">
                          <div class="btn-group" role="group">
                            <router-link
                              :to="`/gastos/${gasto.id}/editar`"
                              class="btn btn-sm btn-outline-warning"
                              title="Editar"
                            >
                              <i class="fas fa-edit"></i>
                            </router-link>
                            <button
                              @click="confirmarEliminacion(gasto)"
                              class="btn btn-sm btn-outline-danger"
                              title="Eliminar"
                            >
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>

                          <!-- Acción de pago -->
                          <div v-if="gasto.estado === 'pendiente' || gasto.estado === 'vencido'">
                            <button
                              @click="marcarComoPagado(gasto)"
                              class="btn btn-sm btn-outline-success"
                              title="Marcar como pagado"
                              :disabled="isMarkingPaid === gasto.id"
                            >
                              <i class="fas fa-check" v-if="isMarkingPaid !== gasto.id"></i>
                              <div v-else class="spinner-border spinner-border-sm" role="status"></div>
                              <span class="ms-1">Pagar</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Paginación -->
            <div v-if="totalPages > 1" class="card-footer">
              <nav aria-label="Paginación de gastos">
                <ul class="pagination justify-content-center mb-0">
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="goToPage(1)" :disabled="currentPage === 1">
                      <i class="fas fa-angle-double-left"></i>
                    </button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <button class="page-link" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">
                      <i class="fas fa-angle-left"></i>
                    </button>
                  </li>

                  <li
                    v-for="page in visiblePages"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === currentPage }"
                  >
                    <button class="page-link" @click="goToPage(page)">{{ page }}</button>
                  </li>

                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">
                      <i class="fas fa-angle-right"></i>
                    </button>
                  </li>
                  <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <button class="page-link" @click="goToPage(totalPages)" :disabled="currentPage === totalPages">
                      <i class="fas fa-angle-double-right"></i>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmación de eliminación -->
    <div class="modal fade" id="deleteModal" tabindex="-1" ref="deleteModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmar eliminación</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>¿Estás seguro de que deseas eliminar el gasto <strong>{{ gastoAEliminar?.concepto }}</strong>?</p>
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Esta acción no se puede deshacer.
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button
              type="button"
              class="btn btn-danger"
              @click="eliminarGasto"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="spinner-border spinner-border-sm me-2"></span>
              {{ isDeleting ? 'Eliminando...' : 'Eliminar gasto' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de marcar como pagado -->
    <div class="modal fade" id="payModal" tabindex="-1" ref="payModal">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Marcar como Pagado</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>¿Confirmas que el gasto <strong>{{ gastoAPagar?.concepto }}</strong> ha sido pagado?</p>
            <div class="mb-3">
              <label class="form-label">Fecha de pago</label>
              <input
                v-model="fechaPago"
                type="date"
                class="form-control"
                :max="today"
              >
            </div>
            <div class="mb-3">
              <label class="form-label">Observaciones (opcional)</label>
              <textarea
                v-model="observacionesPago"
                class="form-control"
                rows="3"
                placeholder="Añade cualquier observación sobre el pago..."
              ></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            <button
              type="button"
              class="btn btn-success"
              @click="confirmarPago"
              :disabled="isMarkingPaid || !fechaPago"
            >
              <span v-if="isMarkingPaid" class="spinner-border spinner-border-sm me-2"></span>
              {{ isMarkingPaid ? 'Procesando...' : 'Confirmar pago' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { gastosService } from '@/services/gastos'
import { formatFecha } from '@/utils/formatters'
import { Modal } from 'bootstrap'
import moment from 'moment'

export default {
  name: 'GastosList',
  setup() {
    const toast = useToast()

    // Estado reactivo
    const gastos = ref([])
    const isLoading = ref(false)
    const isDeleting = ref(false)
    const isMarkingPaid = ref(null)
    const searchTerm = ref('')
    const filterCategoria = ref('')
    const filterEstado = ref('')
    const filterFechaDesde = ref('')
    const filterFechaHasta = ref('')
    const filterImporteMin = ref('')
    const viewMode = ref('table')
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const gastoAEliminar = ref(null)
    const gastoAPagar = ref(null)
    const fechaPago = ref('')
    const observacionesPago = ref('')
    const deleteModal = ref(null)
    const payModal = ref(null)

    // Computed properties
    const today = computed(() => moment().format('YYYY-MM-DD'))

    const filteredGastos = computed(() => {
      let filtered = Array.isArray(gastos.value) ? gastos.value : []

      // Filtro por búsqueda
      if (searchTerm.value) {
        const search = searchTerm.value.toLowerCase()
        filtered = filtered.filter(gasto =>
          gasto.concepto?.toLowerCase().includes(search) ||
          gasto.descripcion?.toLowerCase().includes(search) ||
          gasto.categoria?.toLowerCase().includes(search)
        )
      }

      // Filtro por categoría
      if (filterCategoria.value) {
        filtered = filtered.filter(gasto => gasto.categoria === filterCategoria.value)
      }

      // Filtro por estado
      if (filterEstado.value) {
        filtered = filtered.filter(gasto => gasto.estado === filterEstado.value)
      }

      // Filtro por fecha
      if (filterFechaDesde.value) {
        filtered = filtered.filter(gasto =>
          moment(gasto.fecha).isSameOrAfter(moment(filterFechaDesde.value))
        )
      }

      if (filterFechaHasta.value) {
        filtered = filtered.filter(gasto =>
          moment(gasto.fecha).isSameOrBefore(moment(filterFechaHasta.value))
        )
      }

      // Filtro por importe mínimo
      if (filterImporteMin.value) {
        filtered = filtered.filter(gasto =>
          parseFloat(gasto.importe) >= parseFloat(filterImporteMin.value)
        )
      }

      return filtered
    })

    const totalGastos = computed(() => Array.isArray(gastos.value) ? gastos.value.length : 0)
    const gastosPendientes = computed(() =>
      Array.isArray(gastos.value) ? gastos.value.filter(g => g.estado === 'pendiente').length : 0
    )
    const gastosPagados = computed(() =>
      Array.isArray(gastos.value) ? gastos.value.filter(g => g.estado === 'pagado').length : 0
    )
    const gastosVencidos = computed(() =>
      Array.isArray(gastos.value) ? gastos.value.filter(g => g.estado === 'vencido').length : 0
    )

    const totalImporte = computed(() => {
      return Array.isArray(gastos.value) ?
        gastos.value.reduce((total, gasto) => total + parseFloat(gasto.importe || 0), 0) : 0
    })

    const importePagado = computed(() => {
      return Array.isArray(gastos.value) ?
        gastos.value
          .filter(g => g.estado === 'pagado')
          .reduce((total, gasto) => total + parseFloat(gasto.importe || 0), 0) : 0
    })

    const importePendiente = computed(() => {
      return Array.isArray(gastos.value) ?
        gastos.value
          .filter(g => g.estado === 'pendiente' || g.estado === 'vencido')
          .reduce((total, gasto) => total + parseFloat(gasto.importe || 0), 0) : 0
    })

    const hasFilters = computed(() => {
      return searchTerm.value || filterCategoria.value || filterEstado.value ||
             filterFechaDesde.value || filterFechaHasta.value || filterImporteMin.value
    })

    const totalPages = computed(() => Math.ceil(filteredGastos.value.length / itemsPerPage.value))
    const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage.value)
    const endIndex = computed(() => startIndex.value + itemsPerPage.value)

    const paginatedGastos = computed(() => {
      return filteredGastos.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) pages.push(i)
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    // Métodos
    const loadGastos = async () => {
      isLoading.value = true
      try {
        const response = await gastosService.getAll()
        gastos.value = Array.isArray(response.data.data) ? response.data.data :
                      Array.isArray(response.data) ? response.data : []
      } catch (error) {
        console.error('Error al cargar gastos:', error)
        toast.error('Error al cargar la lista de gastos')
        gastos.value = []
      } finally {
        isLoading.value = false
      }
    }

    const clearFilters = () => {
      searchTerm.value = ''
      filterCategoria.value = ''
      filterEstado.value = ''
      filterFechaDesde.value = ''
      filterFechaHasta.value = ''
      filterImporteMin.value = ''
      currentPage.value = 1
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const confirmarEliminacion = (gasto) => {
      gastoAEliminar.value = gasto
      const modal = new Modal(deleteModal.value)
      modal.show()
    }

    const eliminarGasto = async () => {
      if (!gastoAEliminar.value) return

      isDeleting.value = true
      try {
        await gastosService.delete(gastoAEliminar.value.id)
        toast.success('Gasto eliminado correctamente')
        await loadGastos()

        const modal = Modal.getInstance(deleteModal.value)
        modal.hide()
        gastoAEliminar.value = null
      } catch (error) {
        console.error('Error al eliminar gasto:', error)
        toast.error('Error al eliminar el gasto')
      } finally {
        isDeleting.value = false
      }
    }

    const marcarComoPagado = (gasto) => {
      gastoAPagar.value = gasto
      fechaPago.value = today.value
      observacionesPago.value = ''
      const modal = new Modal(payModal.value)
      modal.show()
    }

    const confirmarPago = async () => {
      if (!gastoAPagar.value || !fechaPago.value) return

      isMarkingPaid.value = gastoAPagar.value.id
      try {
        const data = {
          fecha_pago: fechaPago.value,
          observaciones: observacionesPago.value
        }

        await gastosService.marcarPagado(gastoAPagar.value.id, data)
        toast.success('Gasto marcado como pagado correctamente')
        await loadGastos()

        const modal = Modal.getInstance(payModal.value)
        modal.hide()
        gastoAPagar.value = null
        fechaPago.value = ''
        observacionesPago.value = ''
      } catch (error) {
        console.error('Error al marcar gasto como pagado:', error)
        toast.error('Error al marcar el gasto como pagado')
      } finally {
        isMarkingPaid.value = null
      }
    }

    const formatPrice = (price) => {
      if (!price && price !== 0) return '0,00 €'
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(price)
    }

    const formatFechaRelativa = (fecha) => {
      if (!fecha) return ''
      return moment(fecha).fromNow()
    }

    const isVencido = (fecha) => {
      if (!fecha) return false
      return moment(fecha).isBefore(moment(), 'day')
    }

    const getCategoriaClass = (categoria) => {
      const classMap = {
        mantenimiento: 'bg-primary',
        limpieza: 'bg-success',
        seguridad: 'bg-warning text-dark',
        suministros: 'bg-info',
        seguros: 'bg-danger',
        administracion: 'bg-secondary',
        reparaciones: 'bg-dark',
        otros: 'bg-light text-dark'
      }
      return classMap[categoria] || 'bg-secondary'
    }

    const getCategoriaText = (categoria) => {
      const textMap = {
        mantenimiento: 'Mantenimiento',
        limpieza: 'Limpieza',
        seguridad: 'Seguridad',
        suministros: 'Suministros',
        seguros: 'Seguros',
        administracion: 'Administración',
        reparaciones: 'Reparaciones',
        otros: 'Otros'
      }
      return textMap[categoria] || categoria
    }

    const getEstadoClass = (estado) => {
      const classMap = {
        pendiente: 'bg-warning text-dark',
        pagado: 'bg-success',
        vencido: 'bg-danger'
      }
      return classMap[estado] || 'bg-secondary'
    }

    const getEstadoText = (estado) => {
      const textMap = {
        pendiente: 'Pendiente',
        pagado: 'Pagado',
        vencido: 'Vencido'
      }
      return textMap[estado] || estado
    }

    // Lifecycle
    onMounted(() => {
      loadGastos()
    })

    return {
      gastos,
      isLoading,
      isDeleting,
      isMarkingPaid,
      searchTerm,
      filterCategoria,
      filterEstado,
      filterFechaDesde,
      filterFechaHasta,
      filterImporteMin,
      viewMode,
      currentPage,
      itemsPerPage,
      gastoAEliminar,
      gastoAPagar,
      fechaPago,
      observacionesPago,
      deleteModal,
      payModal,
      today,
      filteredGastos,
      totalGastos,
      gastosPendientes,
      gastosPagados,
      gastosVencidos,
      totalImporte,
      importePagado,
      importePendiente,
      hasFilters,
      totalPages,
      startIndex,
      endIndex,
      paginatedGastos,
      visiblePages,
      loadGastos,
      clearFilters,
      goToPage,
      confirmarEliminacion,
      eliminarGasto,
      marcarComoPagado,
      confirmarPago,
      formatPrice,
      formatFecha,
      formatFechaRelativa,
      isVencido,
      getCategoriaClass,
      getCategoriaText,
      getEstadoClass,
      getEstadoText
    }
  }
}
</script>

<style scoped>
.gastos-list .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.gastos-list .card-title {
  color: #495057;
  font-weight: 600;
}

.gasto-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e3e6f0;
}

.gasto-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.gasto-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
}

.gasto-card .card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
}

.table td {
  vertical-align: middle;
  border-color: #e3e6f0;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

.btn-group .btn {
  border-radius: 0.375rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.pagination .page-link {
  border-radius: 0.375rem;
  margin: 0 0.125rem;
  border-color: #dee2e6;
  color: #495057;
}

.pagination .page-item.active .page-link {
  background-color: #4e73df;
  border-color: #4e73df;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gastos-list .card-body {
    padding: 1rem;
  }

  .gastos-list .btn-group {
    flex-direction: column;
  }

  .gastos-list .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  .gastos-list .table-responsive {
    font-size: 0.875rem;
  }

  .gasto-card .card-footer .d-flex {
    flex-direction: column;
    gap: 0.5rem;
  }

  .gasto-card .card-footer .btn-group {
    width: 100%;
  }

  .gasto-card .card-footer .btn {
    flex: 1;
  }
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.gasto-card {
  animation: fadeIn 0.3s ease;
}

.table tbody tr {
  animation: fadeIn 0.3s ease;
}
</style>

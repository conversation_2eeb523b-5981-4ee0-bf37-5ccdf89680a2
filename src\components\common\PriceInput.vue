<template>
  <div class="price-input">
    <label v-if="label" class="form-label">{{ label }}</label>
    <div class="input-group">
      <span v-if="showCurrency" class="input-group-text">€</span>
      <input
        ref="inputRef"
        v-model="displayValue"
        type="text"
        class="form-control"
        :class="{ 'is-invalid': error }"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      <span v-if="showPerMonth" class="input-group-text">/mes</span>
    </div>
    
    <div v-if="error" class="invalid-feedback d-block">
      {{ error }}
    </div>
    
    <div v-if="showCalculations && numericValue > 0" class="mt-2">
      <div class="row text-muted small">
        <div v-if="showMonthly" class="col-md-4">
          <strong>Mensual:</strong> {{ formatCurrency(numericValue) }}
        </div>
        <div v-if="showQuarterly" class="col-md-4">
          <strong>Trimestral:</strong> {{ formatCurrency(numericValue * 3) }}
        </div>
        <div v-if="showYearly" class="col-md-4">
          <strong>Anual:</strong> {{ formatCurrency(numericValue * 12) }}
        </div>
      </div>
    </div>
    
    <small v-if="helpText" class="form-text text-muted">
      {{ helpText }}
    </small>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'

export default {
  name: 'PriceInput',
  props: {
    modelValue: {
      type: [String, Number],
      default: null
    },
    label: {
      type: String,
      default: null
    },
    placeholder: {
      type: String,
      default: '0,00'
    },
    error: {
      type: String,
      default: null
    },
    helpText: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    showCurrency: {
      type: Boolean,
      default: true
    },
    showPerMonth: {
      type: Boolean,
      default: false
    },
    showCalculations: {
      type: Boolean,
      default: false
    },
    showMonthly: {
      type: Boolean,
      default: true
    },
    showQuarterly: {
      type: Boolean,
      default: true
    },
    showYearly: {
      type: Boolean,
      default: true
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: null
    },
    step: {
      type: Number,
      default: 0.01
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change', 'validation-change'],
  setup(props, { emit }) {
    const inputRef = ref(null)
    const displayValue = ref('')
    const isFocused = ref(false)
    
    const numericValue = computed(() => {
      if (!props.modelValue) return 0
      return parseFloat(props.modelValue) || 0
    })
    
    const formatCurrency = (value) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    }
    
    const formatNumber = (value) => {
      return new Intl.NumberFormat('es-ES', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    }
    
    const parseInput = (input) => {
      if (!input) return null
      
      // Remover caracteres no numéricos excepto comas y puntos
      let cleaned = input.replace(/[^\d,.-]/g, '')
      
      // Reemplazar comas por puntos para el parsing
      cleaned = cleaned.replace(',', '.')
      
      const parsed = parseFloat(cleaned)
      return isNaN(parsed) ? null : parsed
    }
    
    const validateValue = (value) => {
      if (props.required && (value === null || value === undefined)) {
        emit('validation-change', { isValid: false, error: 'Este campo es obligatorio' })
        return false
      }
      
      if (value !== null && value < props.min) {
        emit('validation-change', { isValid: false, error: `El valor mínimo es ${formatCurrency(props.min)}` })
        return false
      }
      
      if (value !== null && props.max !== null && value > props.max) {
        emit('validation-change', { isValid: false, error: `El valor máximo es ${formatCurrency(props.max)}` })
        return false
      }
      
      emit('validation-change', { isValid: true, error: null })
      return true
    }
    
    const updateDisplayValue = () => {
      if (isFocused.value) {
        // Cuando está enfocado, mostrar el valor sin formato para edición
        displayValue.value = numericValue.value > 0 ? numericValue.value.toString().replace('.', ',') : ''
      } else {
        // Cuando no está enfocado, mostrar el valor formateado
        displayValue.value = numericValue.value > 0 ? formatNumber(numericValue.value) : ''
      }
    }
    
    const handleInput = (event) => {
      const inputValue = event.target.value
      const parsed = parseInput(inputValue)
      
      if (parsed !== null) {
        validateValue(parsed)
        emit('update:modelValue', parsed)
        emit('change', parsed)
      } else if (inputValue === '') {
        validateValue(null)
        emit('update:modelValue', null)
        emit('change', null)
      }
    }
    
    const handleFocus = () => {
      isFocused.value = true
      updateDisplayValue()
      
      // Seleccionar todo el texto al enfocar
      nextTick(() => {
        if (inputRef.value) {
          inputRef.value.select()
        }
      })
    }
    
    const handleBlur = () => {
      isFocused.value = false
      updateDisplayValue()
      
      // Validar el valor final
      validateValue(numericValue.value)
    }
    
    // Sincronizar con modelValue externo
    watch(() => props.modelValue, () => {
      updateDisplayValue()
    })
    
    // Inicializar display value
    updateDisplayValue()
    
    return {
      inputRef,
      displayValue,
      numericValue,
      formatCurrency,
      handleInput,
      handleFocus,
      handleBlur
    }
  }
}
</script>

<style scoped>
.price-input .input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
  color: #6c757d;
  font-weight: 500;
}

.price-input .form-control:focus + .input-group-text,
.price-input .input-group-text + .form-control:focus {
  border-color: #86b7fe;
}

.price-input .row {
  font-size: 0.875rem;
}

.price-input .row .col-md-4 {
  margin-bottom: 0.25rem;
}
</style>

<template>
  <div class="date-range-picker">
    <div class="row">
      <div class="col-md-6">
        <label v-if="startLabel" class="form-label">{{ startLabel }}</label>
        <input
          v-model="startDate"
          type="date"
          class="form-control"
          :class="{ 'is-invalid': startError }"
          :disabled="disabled"
          :min="minStartDate"
          :max="maxStartDate"
          @change="handleStartDateChange"
        />
        <div v-if="startError" class="invalid-feedback">
          {{ startError }}
        </div>
      </div>
      
      <div class="col-md-6">
        <label v-if="endLabel" class="form-label">{{ endLabel }}</label>
        <input
          v-model="endDate"
          type="date"
          class="form-control"
          :class="{ 'is-invalid': endError }"
          :disabled="disabled || !startDate"
          :min="minEndDate"
          :max="maxEndDate"
          @change="handleEndDateChange"
        />
        <div v-if="endError" class="invalid-feedback">
          {{ endError }}
        </div>
      </div>
    </div>
    
    <!-- Información del rango -->
    <div v-if="showRangeInfo && startDate && endDate" class="mt-2">
      <div class="alert alert-info py-2">
        <small>
          <i class="fas fa-info-circle me-1"></i>
          <strong>Duración:</strong> {{ getDurationText() }}
          <span v-if="showMonthlyInfo" class="ms-3">
            <strong>Meses completos:</strong> {{ getMonthsCount() }}
          </span>
        </small>
      </div>
    </div>
    
    <!-- Botones de acceso rápido -->
    <div v-if="showQuickButtons" class="mt-2">
      <div class="btn-group btn-group-sm" role="group">
        <button
          type="button"
          class="btn btn-outline-secondary"
          @click="setQuickRange('1month')"
          :disabled="disabled"
        >
          1 Mes
        </button>
        <button
          type="button"
          class="btn btn-outline-secondary"
          @click="setQuickRange('3months')"
          :disabled="disabled"
        >
          3 Meses
        </button>
        <button
          type="button"
          class="btn btn-outline-secondary"
          @click="setQuickRange('6months')"
          :disabled="disabled"
        >
          6 Meses
        </button>
        <button
          type="button"
          class="btn btn-outline-secondary"
          @click="setQuickRange('1year')"
          :disabled="disabled"
        >
          1 Año
        </button>
      </div>
    </div>
    
    <small v-if="helpText" class="form-text text-muted mt-2 d-block">
      {{ helpText }}
    </small>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import moment from 'moment'

export default {
  name: 'DateRangePicker',
  props: {
    modelValue: {
      type: Object,
      default: () => ({ start: null, end: null })
    },
    startLabel: {
      type: String,
      default: 'Fecha de inicio'
    },
    endLabel: {
      type: String,
      default: 'Fecha de fin'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    minStartDate: {
      type: String,
      default: null
    },
    maxStartDate: {
      type: String,
      default: null
    },
    minEndDate: {
      type: String,
      default: null
    },
    maxEndDate: {
      type: String,
      default: null
    },
    showRangeInfo: {
      type: Boolean,
      default: true
    },
    showMonthlyInfo: {
      type: Boolean,
      default: true
    },
    showQuickButtons: {
      type: Boolean,
      default: true
    },
    helpText: {
      type: String,
      default: null
    },
    validateRange: {
      type: Boolean,
      default: true
    },
    minDurationDays: {
      type: Number,
      default: 1
    },
    maxDurationDays: {
      type: Number,
      default: null
    }
  },
  emits: ['update:modelValue', 'change', 'validation-change'],
  setup(props, { emit }) {
    const startDate = ref(props.modelValue?.start || null)
    const endDate = ref(props.modelValue?.end || null)
    const startError = ref(null)
    const endError = ref(null)
    
    const computedMinEndDate = computed(() => {
      if (props.minEndDate) return props.minEndDate
      if (startDate.value) {
        const minEnd = moment(startDate.value).add(props.minDurationDays - 1, 'days')
        return minEnd.format('YYYY-MM-DD')
      }
      return null
    })
    
    const validateDates = () => {
      startError.value = null
      endError.value = null
      
      // Validar fecha de inicio requerida
      if (props.required && !startDate.value) {
        startError.value = 'La fecha de inicio es obligatoria'
      }
      
      // Validar fecha de fin requerida
      if (props.required && !endDate.value) {
        endError.value = 'La fecha de fin es obligatoria'
      }
      
      // Validar rango si ambas fechas están presentes
      if (startDate.value && endDate.value && props.validateRange) {
        const start = moment(startDate.value)
        const end = moment(endDate.value)
        
        if (end.isBefore(start)) {
          endError.value = 'La fecha de fin debe ser posterior a la fecha de inicio'
        }
        
        // Validar duración mínima
        const duration = end.diff(start, 'days') + 1
        if (duration < props.minDurationDays) {
          endError.value = `La duración mínima es de ${props.minDurationDays} día(s)`
        }
        
        // Validar duración máxima
        if (props.maxDurationDays && duration > props.maxDurationDays) {
          endError.value = `La duración máxima es de ${props.maxDurationDays} días`
        }
      }
      
      const isValid = !startError.value && !endError.value
      emit('validation-change', { isValid, errors: { start: startError.value, end: endError.value } })
      
      return isValid
    }
    
    const updateModelValue = () => {
      const value = {
        start: startDate.value,
        end: endDate.value
      }
      emit('update:modelValue', value)
      emit('change', value)
    }
    
    const handleStartDateChange = () => {
      // Si la fecha de fin es anterior a la nueva fecha de inicio, limpiarla
      if (endDate.value && startDate.value && moment(endDate.value).isBefore(moment(startDate.value))) {
        endDate.value = null
      }
      
      validateDates()
      updateModelValue()
    }
    
    const handleEndDateChange = () => {
      validateDates()
      updateModelValue()
    }
    
    const getDurationText = () => {
      if (!startDate.value || !endDate.value) return ''
      
      const start = moment(startDate.value)
      const end = moment(endDate.value)
      const days = end.diff(start, 'days') + 1
      
      if (days === 1) return '1 día'
      if (days < 30) return `${days} días`
      
      const months = end.diff(start, 'months')
      const remainingDays = end.diff(start.clone().add(months, 'months'), 'days')
      
      if (months === 0) return `${days} días`
      if (remainingDays === 0) return `${months} ${months === 1 ? 'mes' : 'meses'}`
      
      return `${months} ${months === 1 ? 'mes' : 'meses'} y ${remainingDays} ${remainingDays === 1 ? 'día' : 'días'}`
    }
    
    const getMonthsCount = () => {
      if (!startDate.value || !endDate.value) return 0
      
      const start = moment(startDate.value)
      const end = moment(endDate.value)
      return end.diff(start, 'months')
    }
    
    const setQuickRange = (range) => {
      const today = moment()
      startDate.value = today.format('YYYY-MM-DD')
      
      switch (range) {
        case '1month':
          endDate.value = today.add(1, 'month').subtract(1, 'day').format('YYYY-MM-DD')
          break
        case '3months':
          endDate.value = today.add(3, 'months').subtract(1, 'day').format('YYYY-MM-DD')
          break
        case '6months':
          endDate.value = today.add(6, 'months').subtract(1, 'day').format('YYYY-MM-DD')
          break
        case '1year':
          endDate.value = today.add(1, 'year').subtract(1, 'day').format('YYYY-MM-DD')
          break
      }
      
      validateDates()
      updateModelValue()
    }
    
    // Sincronizar con modelValue externo
    watch(() => props.modelValue, (newValue) => {
      if (newValue) {
        startDate.value = newValue.start || null
        endDate.value = newValue.end || null
        validateDates()
      }
    }, { deep: true })
    
    // Validar al montar
    validateDates()
    
    return {
      startDate,
      endDate,
      startError,
      endError,
      computedMinEndDate,
      handleStartDateChange,
      handleEndDateChange,
      getDurationText,
      getMonthsCount,
      setQuickRange
    }
  }
}
</script>

<style scoped>
.date-range-picker .alert {
  margin-bottom: 0;
}

.date-range-picker .btn-group {
  flex-wrap: wrap;
}

.date-range-picker .btn-group .btn {
  margin-bottom: 0.25rem;
}
</style>

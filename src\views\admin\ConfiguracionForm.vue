<template>
  <div class="configuracion-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">Configuración del Sistema</h1>
          <p class="text-muted">Configura los parámetros generales de la aplicación</p>
        </div>
      </div>
      
      <form @submit.prevent="handleSubmit">
        <div class="row">
          <!-- Configuración general -->
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-cog me-2"></i>Configuración General
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Nombre de la empresa</label>
                  <input 
                    v-model="form.empresa_nombre" 
                    type="text" 
                    class="form-control" 
                    :class="{ 'is-invalid': errors.empresa_nombre }"
                    placeholder="Ej: Barna Trasteros S.L."
                  >
                  <div v-if="errors.empresa_nombre" class="invalid-feedback">
                    {{ errors.empresa_nombre }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">CIF/NIF</label>
                  <input 
                    v-model="form.empresa_cif" 
                    type="text" 
                    class="form-control" 
                    :class="{ 'is-invalid': errors.empresa_cif }"
                    placeholder="B12345678"
                  >
                  <div v-if="errors.empresa_cif" class="invalid-feedback">
                    {{ errors.empresa_cif }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Dirección</label>
                  <textarea 
                    v-model="form.empresa_direccion" 
                    class="form-control" 
                    :class="{ 'is-invalid': errors.empresa_direccion }"
                    rows="3"
                    placeholder="Dirección completa de la empresa"
                  ></textarea>
                  <div v-if="errors.empresa_direccion" class="invalid-feedback">
                    {{ errors.empresa_direccion }}
                  </div>
                </div>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Teléfono</label>
                    <input 
                      v-model="form.empresa_telefono" 
                      type="tel" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.empresa_telefono }"
                      placeholder="93 123 45 67"
                    >
                    <div v-if="errors.empresa_telefono" class="invalid-feedback">
                      {{ errors.empresa_telefono }}
                    </div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Email</label>
                    <input 
                      v-model="form.empresa_email" 
                      type="email" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.empresa_email }"
                      placeholder="<EMAIL>"
                    >
                    <div v-if="errors.empresa_email" class="invalid-feedback">
                      {{ errors.empresa_email }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Configuración de alquileres -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-file-contract me-2"></i>Configuración de Alquileres
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Duración mínima (meses)</label>
                    <input 
                      v-model="form.alquiler_duracion_minima" 
                      type="number" 
                      min="1" 
                      max="60"
                      class="form-control" 
                      :class="{ 'is-invalid': errors.alquiler_duracion_minima }"
                    >
                    <div v-if="errors.alquiler_duracion_minima" class="invalid-feedback">
                      {{ errors.alquiler_duracion_minima }}
                    </div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Fianza por defecto (meses)</label>
                    <select 
                      v-model="form.alquiler_fianza_meses" 
                      class="form-select" 
                      :class="{ 'is-invalid': errors.alquiler_fianza_meses }"
                    >
                      <option value="1">1 mes</option>
                      <option value="2">2 meses</option>
                      <option value="3">3 meses</option>
                    </select>
                    <div v-if="errors.alquiler_fianza_meses" class="invalid-feedback">
                      {{ errors.alquiler_fianza_meses }}
                    </div>
                  </div>
                </div>
                
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Día de cobro por defecto</label>
                    <select 
                      v-model="form.alquiler_dia_cobro_defecto" 
                      class="form-select" 
                      :class="{ 'is-invalid': errors.alquiler_dia_cobro_defecto }"
                    >
                      <option v-for="dia in diasDelMes" :key="dia" :value="dia">
                        Día {{ dia }}
                      </option>
                    </select>
                    <div v-if="errors.alquiler_dia_cobro_defecto" class="invalid-feedback">
                      {{ errors.alquiler_dia_cobro_defecto }}
                    </div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Incremento anual (%)</label>
                    <input 
                      v-model="form.alquiler_incremento_anual" 
                      type="number" 
                      step="0.1" 
                      min="0" 
                      max="20"
                      class="form-control" 
                      :class="{ 'is-invalid': errors.alquiler_incremento_anual }"
                      placeholder="2.5"
                    >
                    <div v-if="errors.alquiler_incremento_anual" class="invalid-feedback">
                      {{ errors.alquiler_incremento_anual }}
                    </div>
                  </div>
                </div>
                
                <div class="form-check">
                  <input 
                    v-model="form.alquiler_renovacion_automatica" 
                    class="form-check-input" 
                    type="checkbox" 
                    id="renovacion_automatica"
                  >
                  <label class="form-check-label" for="renovacion_automatica">
                    Permitir renovación automática de contratos
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Configuración de notificaciones -->
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-bell me-2"></i>Notificaciones
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Días de aviso antes del vencimiento</label>
                  <input 
                    v-model="form.notif_dias_vencimiento" 
                    type="number" 
                    min="1" 
                    max="90"
                    class="form-control" 
                    :class="{ 'is-invalid': errors.notif_dias_vencimiento }"
                    placeholder="30"
                  >
                  <small class="form-text text-muted">
                    Días antes del vencimiento para enviar recordatorio
                  </small>
                  <div v-if="errors.notif_dias_vencimiento" class="invalid-feedback">
                    {{ errors.notif_dias_vencimiento }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Días de gracia para pagos</label>
                  <input 
                    v-model="form.notif_dias_gracia_pago" 
                    type="number" 
                    min="0" 
                    max="30"
                    class="form-control" 
                    :class="{ 'is-invalid': errors.notif_dias_gracia_pago }"
                    placeholder="5"
                  >
                  <small class="form-text text-muted">
                    Días de gracia antes de marcar un pago como vencido
                  </small>
                  <div v-if="errors.notif_dias_gracia_pago" class="invalid-feedback">
                    {{ errors.notif_dias_gracia_pago }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <h6>Tipos de notificaciones activas:</h6>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.notif_email_vencimientos" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="email_vencimientos"
                    >
                    <label class="form-check-label" for="email_vencimientos">
                      Email de vencimientos de contrato
                    </label>
                  </div>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.notif_email_pagos" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="email_pagos"
                    >
                    <label class="form-check-label" for="email_pagos">
                      Email de recordatorio de pagos
                    </label>
                  </div>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.notif_sms_urgentes" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="sms_urgentes"
                    >
                    <label class="form-check-label" for="sms_urgentes">
                      SMS para notificaciones urgentes
                    </label>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Configuración de documentos -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-file me-2"></i>Documentos
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Tamaño máximo de archivo (MB)</label>
                  <input 
                    v-model="form.doc_tamano_maximo" 
                    type="number" 
                    min="1" 
                    max="100"
                    class="form-control" 
                    :class="{ 'is-invalid': errors.doc_tamano_maximo }"
                    placeholder="10"
                  >
                  <div v-if="errors.doc_tamano_maximo" class="invalid-feedback">
                    {{ errors.doc_tamano_maximo }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Formatos permitidos</label>
                  <div class="form-check">
                    <input 
                      v-model="form.doc_permitir_pdf" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="permitir_pdf"
                    >
                    <label class="form-check-label" for="permitir_pdf">PDF</label>
                  </div>
                  <div class="form-check">
                    <input 
                      v-model="form.doc_permitir_imagenes" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="permitir_imagenes"
                    >
                    <label class="form-check-label" for="permitir_imagenes">Imágenes (JPG, PNG)</label>
                  </div>
                  <div class="form-check">
                    <input 
                      v-model="form.doc_permitir_office" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="permitir_office"
                    >
                    <label class="form-check-label" for="permitir_office">Documentos Office (DOC, XLS)</label>
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Días para eliminar documentos temporales</label>
                  <input 
                    v-model="form.doc_dias_limpieza" 
                    type="number" 
                    min="1" 
                    max="365"
                    class="form-control" 
                    :class="{ 'is-invalid': errors.doc_dias_limpieza }"
                    placeholder="30"
                  >
                  <div v-if="errors.doc_dias_limpieza" class="invalid-feedback">
                    {{ errors.doc_dias_limpieza }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <button 
                type="button" 
                class="btn btn-outline-secondary"
                @click="resetToDefaults"
              >
                <i class="fas fa-undo me-1"></i>Restaurar valores por defecto
              </button>
              <button 
                type="submit" 
                class="btn btn-primary"
                :disabled="isSubmitting"
              >
                <i class="fas fa-save me-1"></i>
                {{ isSubmitting ? 'Guardando...' : 'Guardar configuración' }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'

export default {
  name: 'ConfiguracionForm',
  setup() {
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const errors = ref({})
    
    const form = ref({
      // Empresa
      empresa_nombre: '',
      empresa_cif: '',
      empresa_direccion: '',
      empresa_telefono: '',
      empresa_email: '',
      
      // Alquileres
      alquiler_duracion_minima: 1,
      alquiler_fianza_meses: 2,
      alquiler_dia_cobro_defecto: 1,
      alquiler_incremento_anual: 2.5,
      alquiler_renovacion_automatica: true,
      
      // Notificaciones
      notif_dias_vencimiento: 30,
      notif_dias_gracia_pago: 5,
      notif_email_vencimientos: true,
      notif_email_pagos: true,
      notif_sms_urgentes: false,
      
      // Documentos
      doc_tamano_maximo: 10,
      doc_permitir_pdf: true,
      doc_permitir_imagenes: true,
      doc_permitir_office: true,
      doc_dias_limpieza: 30
    })
    
    const diasDelMes = computed(() => {
      return Array.from({ length: 28 }, (_, i) => i + 1)
    })
    
    const validateForm = () => {
      errors.value = {}

      if (!form.value.empresa_nombre?.trim()) {
        errors.value.empresa_nombre = 'El nombre de la empresa es obligatorio'
      }

      if (form.value.empresa_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.empresa_email)) {
        errors.value.empresa_email = 'El email no tiene un formato válido'
      }

      if (form.value.alquiler_duracion_minima < 1 || form.value.alquiler_duracion_minima > 60) {
        errors.value.alquiler_duracion_minima = 'La duración debe estar entre 1 y 60 meses'
      }

      if (form.value.notif_dias_vencimiento < 1 || form.value.notif_dias_vencimiento > 90) {
        errors.value.notif_dias_vencimiento = 'Los días de aviso deben estar entre 1 y 90'
      }

      if (form.value.doc_tamano_maximo < 1 || form.value.doc_tamano_maximo > 100) {
        errors.value.doc_tamano_maximo = 'El tamaño máximo debe estar entre 1 y 100 MB'
      }

      return Object.keys(errors.value).length === 0
    }

    const resetToDefaults = () => {
      if (confirm('¿Estás seguro de que quieres restaurar los valores por defecto? Se perderán todos los cambios no guardados.')) {
        form.value = {
          empresa_nombre: '',
          empresa_cif: '',
          empresa_direccion: '',
          empresa_telefono: '',
          empresa_email: '',
          alquiler_duracion_minima: 1,
          alquiler_fianza_meses: 2,
          alquiler_dia_cobro_defecto: 1,
          alquiler_incremento_anual: 2.5,
          alquiler_renovacion_automatica: true,
          notif_dias_vencimiento: 30,
          notif_dias_gracia_pago: 5,
          notif_email_vencimientos: true,
          notif_email_pagos: true,
          notif_sms_urgentes: false,
          doc_tamano_maximo: 10,
          doc_permitir_pdf: true,
          doc_permitir_imagenes: true,
          doc_permitir_office: true,
          doc_dias_limpieza: 30
        }

        toast.info('Configuración restaurada a valores por defecto')
      }
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isSubmitting.value = true

      try {
        // Aquí iría la llamada al servicio para guardar la configuración
        // await configuracionService.update(form.value)

        // Simulamos la llamada
        await new Promise(resolve => setTimeout(resolve, 1000))

        toast.success('Configuración guardada correctamente')
      } catch (error) {
        console.error('Error al guardar configuración:', error)
        toast.error('Error al guardar la configuración')
      } finally {
        isSubmitting.value = false
      }
    }

    const loadConfiguration = async () => {
      try {
        // Aquí iría la llamada al servicio para cargar la configuración
        // const response = await configuracionService.get()
        // form.value = response.data

        // Por ahora cargamos valores de ejemplo
        console.log('Cargando configuración...')
      } catch (error) {
        console.error('Error al cargar configuración:', error)
        toast.error('Error al cargar la configuración')
      }
    }

    // Cargar configuración al montar el componente
    onMounted(() => {
      loadConfiguration()
    })

    return {
      form,
      errors,
      isSubmitting,
      diasDelMes,
      validateForm,
      resetToDefaults,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.configuracion-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.configuracion-form .card-title {
  color: #495057;
  font-weight: 600;
}

.configuracion-form .form-check {
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.configuracion-form .form-check-input {
  margin-left: -1.5rem;
}

.configuracion-form .form-check-label {
  font-weight: 500;
  color: #495057;
}

.configuracion-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.configuracion-form .gap-2 {
  gap: 0.5rem !important;
}

.configuracion-form .form-control:focus,
.configuracion-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.configuracion-form .is-invalid {
  border-color: #dc3545;
}

.configuracion-form .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.configuracion-form .form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.configuracion-form h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .configuracion-form .card-body {
    padding: 1rem;
  }

  .configuracion-form .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .configuracion-form .d-flex {
    flex-direction: column;
  }
}
</style>

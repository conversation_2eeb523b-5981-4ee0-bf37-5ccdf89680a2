import api from './api'

export const alquileresService = {
  // Obtener todos los alquileres
  getAll(params = {}) {
    return api.get('/alquileres', { params })
  },

  // Obtener alquiler por ID
  getById(id) {
    return api.get(`/alquileres/${id}`)
  },

  // Crear nuevo alquiler
  create(data) {
    return api.post('/alquileres', data)
  },

  // Actualizar alquiler
  update(id, data) {
    return api.put(`/alquileres/${id}`, data)
  },

  // Eliminar alquiler
  delete(id) {
    return api.delete(`/alquileres/${id}`)
  },

  // Obtener alquileres pendientes
  getPendientes() {
    return api.get('/alquileres-pendientes')
  },

  // Obtener alquileres vencidos
  getVencidos() {
    return api.get('/alquileres-vencidos')
  },

  // Registrar pago
  registrarPago(id, data) {
    return api.post(`/alquileres/${id}/pago`, data)
  },

  // Verificar estado de pagos
  verificarEstados() {
    return api.post('/alquileres/verificar-estados')
  },

  // Obtener historial de pagos
  getHistorialPagos(id) {
    return api.get(`/alquileres/${id}/historial-pagos`)
  },

  // Generar recibo
  generarRecibo(id) {
    return api.get(`/alquileres/${id}/recibo`, {
      responseType: 'blob'
    })
  },

  // Obtener alquileres por cliente
  getPorCliente(clienteId) {
    return api.get(`/alquileres-cliente/${clienteId}`)
  },

  // Obtener alquileres por propiedad
  getPorPropiedad(tipo, propiedadId) {
    return api.get(`/alquileres-propiedad/${tipo}/${propiedadId}`)
  }
}

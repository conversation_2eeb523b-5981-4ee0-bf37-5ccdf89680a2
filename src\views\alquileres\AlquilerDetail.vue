<template>
  <div class="alquiler-detail">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Detalle del Contrato</h1>
              <p class="text-muted mb-0">Información completa del contrato de alquiler</p>
            </div>
            <div class="btn-group">
              <router-link 
                :to="`/alquileres/${id}/editar`" 
                class="btn btn-outline-primary"
              >
                <i class="fas fa-edit me-1"></i>Editar
              </router-link>
              <button 
                class="btn btn-outline-success"
                @click="showPagoForm = true"
              >
                <i class="fas fa-plus me-1"></i>Registrar pago
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="alquiler" class="row">
        <!-- Información principal -->
        <div class="col-lg-8">
          <!-- Datos del contrato -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="fas fa-file-contract me-2"></i>Información del Contrato
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="info-group">
                    <label>Cliente:</label>
                    <div class="fw-bold">{{ alquiler.cliente?.nombre }} {{ alquiler.cliente?.apellidos }}</div>
                    <small class="text-muted">{{ alquiler.cliente?.dni }} | {{ alquiler.cliente?.telefono }}</small>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-group">
                    <label>Propiedad:</label>
                    <div class="fw-bold">{{ formatPropiedad() }}</div>
                    <small class="text-muted">{{ formatPrecio(alquiler.valor) }}/mes</small>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-group">
                    <label>Período del contrato:</label>
                    <div>{{ formatFecha(alquiler.fecha_inicio) }} - {{ formatFecha(alquiler.fecha_fin) }}</div>
                    <small class="text-muted">{{ getDuracion() }}</small>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="info-group">
                    <label>Estado:</label>
                    <span :class="getEstadoClass()">{{ alquiler.estado }}</span>
                  </div>
                </div>
              </div>
              
              <div v-if="alquiler.observaciones" class="row mt-3">
                <div class="col-12">
                  <div class="info-group">
                    <label>Observaciones:</label>
                    <div>{{ alquiler.observaciones }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Historial de pagos -->
          <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="fas fa-history me-2"></i>Historial de Pagos
              </h5>
              <button 
                class="btn btn-sm btn-outline-primary"
                @click="loadHistorialPagos"
              >
                <i class="fas fa-sync-alt me-1"></i>Actualizar
              </button>
            </div>
            <div class="card-body">
              <div v-if="isLoadingPagos" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
              </div>
              
              <div v-else-if="historialPagos.length === 0" class="text-center py-4 text-muted">
                <i class="fas fa-receipt fa-3x mb-3"></i>
                <p>No hay pagos registrados para este contrato</p>
                <button 
                  class="btn btn-primary"
                  @click="showPagoForm = true"
                >
                  <i class="fas fa-plus me-1"></i>Registrar primer pago
                </button>
              </div>
              
              <div v-else class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Fecha</th>
                      <th>Importe</th>
                      <th>Método</th>
                      <th>Concepto</th>
                      <th>Estado</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="pago in historialPagos" :key="pago.id">
                      <td>{{ formatFecha(pago.fecha_pago) }}</td>
                      <td class="fw-bold">{{ formatPrecio(pago.importe) }}</td>
                      <td>{{ pago.metodo_pago }}</td>
                      <td>{{ pago.concepto }}</td>
                      <td>
                        <span :class="getPagoEstadoClass(pago.estado)">
                          {{ pago.estado }}
                        </span>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button 
                            class="btn btn-outline-primary"
                            @click="verRecibo(pago)"
                            title="Ver recibo"
                          >
                            <i class="fas fa-receipt"></i>
                          </button>
                          <button 
                            class="btn btn-outline-secondary"
                            @click="editarPago(pago)"
                            title="Editar pago"
                          >
                            <i class="fas fa-edit"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- Panel lateral -->
        <div class="col-lg-4">
          <!-- Estado financiero -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="fas fa-chart-line me-2"></i>Estado Financiero
              </h5>
            </div>
            <div class="card-body">
              <div class="row text-center">
                <div class="col-6">
                  <div class="stat-item">
                    <div class="stat-value text-success">{{ formatPrecio(totalPagado) }}</div>
                    <div class="stat-label">Total pagado</div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="stat-item">
                    <div class="stat-value text-primary">{{ formatPrecio(totalEsperado) }}</div>
                    <div class="stat-label">Total esperado</div>
                  </div>
                </div>
              </div>
              
              <hr>
              
              <div class="row text-center">
                <div class="col-12">
                  <div class="stat-item">
                    <div :class="getSaldoClass()">{{ formatPrecio(saldo) }}</div>
                    <div class="stat-label">{{ saldo >= 0 ? 'Saldo a favor' : 'Saldo pendiente' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Acciones rápidas -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="fas fa-bolt me-2"></i>Acciones Rápidas
              </h5>
            </div>
            <div class="card-body">
              <div class="d-grid gap-2">
                <button 
                  class="btn btn-success"
                  @click="showPagoForm = true"
                >
                  <i class="fas fa-plus me-1"></i>Registrar pago
                </button>
                <button 
                  class="btn btn-outline-primary"
                  @click="generarRecibo"
                >
                  <i class="fas fa-receipt me-1"></i>Generar recibo
                </button>
                <button 
                  class="btn btn-outline-info"
                  @click="enviarRecordatorio"
                >
                  <i class="fas fa-envelope me-1"></i>Enviar recordatorio
                </button>
                <router-link 
                  :to="`/alquileres/${id}/editar`"
                  class="btn btn-outline-warning"
                >
                  <i class="fas fa-edit me-1"></i>Editar contrato
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading state -->
      <div v-else class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <p class="mt-3 text-muted">Cargando información del contrato...</p>
      </div>
    </div>

    <!-- Modal para registrar pago -->
    <div v-if="showPagoForm" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Registrar Nuevo Pago</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showPagoForm = false"
            ></button>
          </div>
          <div class="modal-body">
            <!-- Aquí iría el formulario de pago -->
            <p class="text-muted">Formulario de registro de pago (por implementar)</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { alquileresService } from '@/services/alquileres'
import moment from 'moment'

export default {
  name: 'AlquilerDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    const toast = useToast()
    
    const alquiler = ref(null)
    const historialPagos = ref([])
    const isLoadingPagos = ref(false)
    const showPagoForm = ref(false)
    
    const totalPagado = computed(() => {
      return historialPagos.value
        .filter(pago => pago.estado === 'confirmado')
        .reduce((total, pago) => total + parseFloat(pago.importe), 0)
    })
    
    const totalEsperado = computed(() => {
      if (!alquiler.value) return 0
      
      const inicio = moment(alquiler.value.fecha_inicio)
      const fin = moment(alquiler.value.fecha_fin)
      const meses = fin.diff(inicio, 'months') + 1
      
      return meses * alquiler.value.valor
    })
    
    const saldo = computed(() => {
      return totalPagado.value - totalEsperado.value
    })
    
    const formatPropiedad = () => {
      if (!alquiler.value) return ''

      if (alquiler.value.tipo_propiedad === 'App\\Trastero') {
        return `Trastero ${alquiler.value.propiedad?.numero || alquiler.value.propiedad_id}`
      } else {
        return `Piso ${alquiler.value.propiedad?.direccion || alquiler.value.propiedad_id}`
      }
    }

    const formatPrecio = (precio) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(precio)
    }

    const formatFecha = (fecha) => {
      return moment(fecha).format('DD/MM/YYYY')
    }

    const getDuracion = () => {
      if (!alquiler.value) return ''

      const inicio = moment(alquiler.value.fecha_inicio)
      const fin = moment(alquiler.value.fecha_fin)
      const meses = fin.diff(inicio, 'months')

      return `${meses} ${meses === 1 ? 'mes' : 'meses'}`
    }

    const getEstadoClass = () => {
      if (!alquiler.value) return ''

      const classes = {
        'activo': 'badge bg-success',
        'pendiente': 'badge bg-warning',
        'vencido': 'badge bg-danger',
        'finalizado': 'badge bg-secondary',
        'borrador': 'badge bg-info'
      }
      return classes[alquiler.value.estado] || 'badge bg-secondary'
    }

    const getPagoEstadoClass = (estado) => {
      const classes = {
        'confirmado': 'badge bg-success',
        'pendiente': 'badge bg-warning',
        'rechazado': 'badge bg-danger'
      }
      return classes[estado] || 'badge bg-secondary'
    }

    const getSaldoClass = () => {
      if (saldo.value > 0) return 'stat-value text-success'
      if (saldo.value < 0) return 'stat-value text-danger'
      return 'stat-value text-muted'
    }

    const loadHistorialPagos = async () => {
      isLoadingPagos.value = true
      try {
        const response = await alquileresService.getHistorialPagos(props.id)
        historialPagos.value = response.data
      } catch (error) {
        console.error('Error al cargar historial de pagos:', error)
        toast.error('Error al cargar el historial de pagos')
      } finally {
        isLoadingPagos.value = false
      }
    }

    const verRecibo = (pago) => {
      // Implementar vista de recibo
      toast.info('Función de ver recibo por implementar')
    }

    const editarPago = (pago) => {
      // Implementar edición de pago
      toast.info('Función de editar pago por implementar')
    }

    const generarRecibo = () => {
      // Implementar generación de recibo
      toast.info('Función de generar recibo por implementar')
    }

    const enviarRecordatorio = () => {
      // Implementar envío de recordatorio
      toast.info('Función de enviar recordatorio por implementar')
    }

    // Cargar datos al montar el componente
    onMounted(async () => {
      try {
        const response = await alquileresService.getById(props.id)
        alquiler.value = response.data
        await loadHistorialPagos()
      } catch (error) {
        console.error('Error al cargar alquiler:', error)
        toast.error('Error al cargar los datos del contrato')
        router.push('/alquileres')
      }
    })

    return {
      alquiler,
      historialPagos,
      isLoadingPagos,
      showPagoForm,
      totalPagado,
      totalEsperado,
      saldo,
      formatPropiedad,
      formatPrecio,
      formatFecha,
      getDuracion,
      getEstadoClass,
      getPagoEstadoClass,
      getSaldoClass,
      loadHistorialPagos,
      verRecibo,
      editarPago,
      generarRecibo,
      enviarRecordatorio
    }
  }
}
</script>

<style scoped>
.alquiler-detail .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.alquiler-detail .card-title {
  color: #495057;
  font-weight: 600;
}

.info-group {
  margin-bottom: 1rem;
}

.info-group label {
  display: block;
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.stat-item {
  padding: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.alquiler-detail .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.alquiler-detail .btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.alquiler-detail .table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.alquiler-detail .table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.alquiler-detail .spinner-border {
  width: 2rem;
  height: 2rem;
}

.alquiler-detail .modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.alquiler-detail .d-grid .btn {
  text-align: left;
}

.alquiler-detail .d-grid .btn i {
  width: 1.2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .alquiler-detail .card-body {
    padding: 1rem;
  }

  .alquiler-detail .btn-group {
    flex-direction: column;
    width: 100%;
  }

  .alquiler-detail .btn-group .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .alquiler-detail .table-responsive {
    font-size: 0.8rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }
}

/* Print styles */
@media print {
  .alquiler-detail .btn,
  .alquiler-detail .btn-group {
    display: none !important;
  }

  .alquiler-detail .card {
    border: 1px solid #000;
    break-inside: avoid;
  }
}
</style>

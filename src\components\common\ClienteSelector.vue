<template>
  <div class="cliente-selector">
    <label v-if="label" class="form-label">{{ label }}</label>
    <div class="input-group">
      <select 
        v-model="selectedCliente" 
        class="form-select" 
        :class="{ 'is-invalid': error }"
        :disabled="disabled || isLoading"
        @change="handleChange"
      >
        <option value="">{{ placeholder }}</option>
        <option 
          v-for="cliente in filteredClientes" 
          :key="cliente.id" 
          :value="cliente.id"
        >
          {{ cliente.nombre }} {{ cliente.apellidos }} - {{ cliente.dni }}
        </option>
      </select>
      
      <button 
        v-if="showCreateButton"
        type="button" 
        class="btn btn-outline-primary" 
        @click="$emit('create-cliente')"
        :disabled="disabled || isLoading"
      >
        <i class="fas fa-plus"></i>
      </button>
      
      <button 
        v-if="showRefreshButton"
        type="button" 
        class="btn btn-outline-secondary" 
        @click="loadClientes"
        :disabled="disabled || isLoading"
      >
        <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoading }"></i>
      </button>
    </div>
    
    <div v-if="error" class="invalid-feedback d-block">
      {{ error }}
    </div>
    
    <small v-if="helpText" class="form-text text-muted">
      {{ helpText }}
    </small>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { clientesService } from '@/services/clientes'
import { useToast } from 'vue-toastification'

export default {
  name: 'ClienteSelector',
  props: {
    modelValue: {
      type: [String, Number],
      default: null
    },
    label: {
      type: String,
      default: 'Cliente'
    },
    placeholder: {
      type: String,
      default: 'Selecciona un cliente...'
    },
    error: {
      type: String,
      default: null
    },
    helpText: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showCreateButton: {
      type: Boolean,
      default: true
    },
    showRefreshButton: {
      type: Boolean,
      default: true
    },
    filterByStatus: {
      type: String,
      default: null // 'activo', 'posible', 'confirmado'
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change', 'create-cliente'],
  setup(props, { emit }) {
    const toast = useToast()
    const clientes = ref([])
    const isLoading = ref(false)
    
    const selectedCliente = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })
    
    const filteredClientes = computed(() => {
      if (!props.filterByStatus) {
        return clientes.value
      }
      
      return clientes.value.filter(cliente => {
        switch (props.filterByStatus) {
          case 'activo':
            return cliente.estado === 'activo'
          case 'posible':
            return cliente.estado === 'posible'
          case 'confirmado':
            return cliente.estado === 'confirmado'
          default:
            return true
        }
      })
    })
    
    const loadClientes = async () => {
      isLoading.value = true
      try {
        const response = await clientesService.getAll()
        clientes.value = response.data
      } catch (error) {
        console.error('Error al cargar clientes:', error)
        toast.error('Error al cargar la lista de clientes')
      } finally {
        isLoading.value = false
      }
    }
    
    const handleChange = () => {
      const cliente = clientes.value.find(c => c.id == selectedCliente.value)
      emit('change', cliente)
    }
    
    // Cargar clientes al montar el componente
    onMounted(() => {
      loadClientes()
    })
    
    // Recargar si cambia el filtro
    watch(() => props.filterByStatus, () => {
      if (clientes.value.length === 0) {
        loadClientes()
      }
    })
    
    return {
      clientes,
      selectedCliente,
      filteredClientes,
      isLoading,
      loadClientes,
      handleChange
    }
  }
}
</script>

<style scoped>
.cliente-selector .input-group {
  flex-wrap: nowrap;
}

.cliente-selector .form-select {
  min-width: 0;
}

.cliente-selector .btn {
  border-left: 0;
}

.cliente-selector .btn:first-of-type {
  border-left: 1px solid #ced4da;
}
</style>

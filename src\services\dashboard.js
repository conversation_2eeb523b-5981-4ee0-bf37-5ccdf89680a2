import api from './api'

export const dashboardService = {
  // Obtener resumen general
  getResumen() {
    return api.get('/dashboard/resumen')
  },

  // Obtener pagos pendientes
  getPagosPendientes() {
    return api.get('/dashboard/pagos-pendientes')
  },

  // Obtener estadísticas de ocupación
  getEstadisticasOcupacion() {
    return api.get('/dashboard/ocupacion')
  },

  // Obtener ingresos mensuales
  getIngresosMensuales(year = null) {
    const params = year ? { year } : {}
    return api.get('/dashboard/ingresos-mensuales', { params })
  },

  // Obtener gastos mensuales
  getGastosMensuales(year = null) {
    const params = year ? { year } : {}
    return api.get('/dashboard/gastos-mensuales', { params })
  },

  // Obtener alertas
  getAlertas() {
    return api.get('/dashboard/alertas')
  },

  // Obtener actividad reciente
  getActividadReciente() {
    return api.get('/dashboard/actividad-reciente')
  },

  // Obtener próximos vencimientos
  getProximosVencimientos() {
    return api.get('/dashboard/proximos-vencimientos')
  },

  // Obtener estadísticas por tipo de propiedad
  getEstadisticasPorTipo() {
    return api.get('/dashboard/estadisticas-tipo')
  }
}

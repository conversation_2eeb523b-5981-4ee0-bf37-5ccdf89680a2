{"name": "barnatrasteros-frontend", "version": "1.0.0", "description": "Frontend Vue.js para BarnaTrasteros - Sistema de gestión de alquileres", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.0", "@vee-validate/i18n": "^4.12.0", "@vee-validate/rules": "^4.12.0", "axios": "^1.6.0", "bootstrap": "^5.3.2", "bootstrap-vue-next": "^0.15.0", "chart.js": "^4.4.0", "lodash-es": "^4.17.21", "moment": "^2.29.4", "pinia": "^2.1.7", "vee-validate": "^4.12.0", "vue": "^3.4.0", "vue-chartjs": "^5.3.0", "vue-router": "^4.5.1", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.0", "vite": "^5.0.0"}}
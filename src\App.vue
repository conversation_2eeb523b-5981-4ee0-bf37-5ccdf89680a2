<template>
  <div id="app">
    <NavBar />
    <div class="d-flex app-content">
      <!-- Sidebar (solo visible en rutas autenticadas) -->
      <Sidebar v-if="isAuthenticated" />
      
      <!-- Main content -->
      <div class="main-content flex-grow-1">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import NavBar from '@/components/layout/NavBar.vue'
import Sidebar from '@/components/layout/Sidebar.vue'

export default {
  name: 'App',
  components: {
    NavBar,
    Sidebar
  },
  setup() {
    const authStore = useAuthStore()
    const isAuthenticated = computed(() => authStore.isAuthenticated)
    
    return {
      isAuthenticated
    }
  }
}
</script>

<style>
/* Compensar la altura de la navbar fija */
.app-content {
  padding-top: 56px; /* Altura típica de una navbar de Bootstrap */
}

.main-content {
  padding: 1.5rem;
  min-height: calc(100vh - 56px);
}

/* Asegurar que el body no tenga padding extra */
body {
  padding-top: 0 !important;
}
</style>


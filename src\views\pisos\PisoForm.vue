<template>
  <div class="piso-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Nuevo' }} Piso</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del piso' : 'Registra un nuevo piso en el sistema' }}</p>
        </div>
      </div>

      <form @submit.prevent="handleSubmit">
        <!-- Información básica -->
        <div class="row">
          <div class="col-lg-8">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-home me-2"></i>Información Básica
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-8">
                    <label class="form-label">Dirección completa *</label>
                    <input
                      v-model="form.direccion"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.direccion }"
                      placeholder="Ej: Calle Mayor, 123"
                      required
                    >
                    <div v-if="errors.direccion" class="invalid-feedback">
                      {{ errors.direccion }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Piso/Puerta *</label>
                    <input
                      v-model="form.piso"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.piso }"
                      placeholder="Ej: 2º A, Bajo"
                      required
                    >
                    <div v-if="errors.piso" class="invalid-feedback">
                      {{ errors.piso }}
                    </div>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-4">
                    <label class="form-label">Código postal</label>
                    <input
                      v-model="form.codigo_postal"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.codigo_postal }"
                      placeholder="08001"
                      maxlength="5"
                    >
                    <div v-if="errors.codigo_postal" class="invalid-feedback">
                      {{ errors.codigo_postal }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Ciudad</label>
                    <input
                      v-model="form.ciudad"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.ciudad }"
                      placeholder="Barcelona"
                    >
                    <div v-if="errors.ciudad" class="invalid-feedback">
                      {{ errors.ciudad }}
                    </div>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Provincia</label>
                    <input
                      v-model="form.provincia"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.provincia }"
                      placeholder="Barcelona"
                    >
                    <div v-if="errors.provincia" class="invalid-feedback">
                      {{ errors.provincia }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Estado y disponibilidad -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-info-circle me-2"></i>Estado
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Estado actual *</label>
                  <select
                    v-model="form.estado"
                    class="form-select"
                    :class="{ 'is-invalid': errors.estado }"
                    required
                  >
                    <option value="disponible">Disponible</option>
                    <option value="ocupado">Ocupado</option>
                    <option value="mantenimiento">En mantenimiento</option>
                    <option value="reservado">Reservado</option>
                    <option value="inactivo">Inactivo</option>
                  </select>
                  <div v-if="errors.estado" class="invalid-feedback">
                    {{ errors.estado }}
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">Fecha disponibilidad</label>
                  <input
                    v-model="form.fecha_disponibilidad"
                    type="date"
                    class="form-control"
                    :class="{ 'is-invalid': errors.fecha_disponibilidad }"
                    :min="today"
                  >
                  <small class="form-text text-muted">
                    Fecha desde la cual estará disponible
                  </small>
                  <div v-if="errors.fecha_disponibilidad" class="invalid-feedback">
                    {{ errors.fecha_disponibilidad }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Características físicas -->
        <div class="row">
          <div class="col-12">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-ruler-combined me-2"></i>Características Físicas
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-3">
                    <label class="form-label">Superficie (m²) *</label>
                    <input
                      v-model="form.superficie"
                      type="number"
                      step="0.01"
                      min="1"
                      class="form-control"
                      :class="{ 'is-invalid': errors.superficie }"
                      placeholder="75.50"
                      required
                    >
                    <div v-if="errors.superficie" class="invalid-feedback">
                      {{ errors.superficie }}
                    </div>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">Habitaciones *</label>
                    <select
                      v-model="form.habitaciones"
                      class="form-select"
                      :class="{ 'is-invalid': errors.habitaciones }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="1">1 habitación</option>
                      <option value="2">2 habitaciones</option>
                      <option value="3">3 habitaciones</option>
                      <option value="4">4 habitaciones</option>
                      <option value="5">5+ habitaciones</option>
                    </select>
                    <div v-if="errors.habitaciones" class="invalid-feedback">
                      {{ errors.habitaciones }}
                    </div>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">Baños *</label>
                    <select
                      v-model="form.banos"
                      class="form-select"
                      :class="{ 'is-invalid': errors.banos }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="1">1 baño</option>
                      <option value="1.5">1 baño + aseo</option>
                      <option value="2">2 baños</option>
                      <option value="2.5">2 baños + aseo</option>
                      <option value="3">3+ baños</option>
                    </select>
                    <div v-if="errors.banos" class="invalid-feedback">
                      {{ errors.banos }}
                    </div>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">Planta</label>
                    <input
                      v-model="form.planta"
                      type="text"
                      class="form-control"
                      :class="{ 'is-invalid': errors.planta }"
                      placeholder="Ej: 2ª, Bajo, Ático"
                    >
                    <div v-if="errors.planta" class="invalid-feedback">
                      {{ errors.planta }}
                    </div>
                  </div>
                </div>

                <!-- Características adicionales -->
                <div class="row mb-3">
                  <div class="col-md-4">
                    <div class="form-check">
                      <input
                        v-model="form.tiene_ascensor"
                        class="form-check-input"
                        type="checkbox"
                        id="ascensor"
                      >
                      <label class="form-check-label" for="ascensor">
                        Tiene ascensor
                      </label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-check">
                      <input
                        v-model="form.tiene_balcon"
                        class="form-check-input"
                        type="checkbox"
                        id="balcon"
                      >
                      <label class="form-check-label" for="balcon">
                        Tiene balcón/terraza
                      </label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-check">
                      <input
                        v-model="form.amueblado"
                        class="form-check-input"
                        type="checkbox"
                        id="amueblado"
                      >
                      <label class="form-check-label" for="amueblado">
                        Amueblado
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Precio y condiciones económicas -->
        <div class="row">
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-euro-sign me-2"></i>Condiciones Económicas
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <PriceInput
                    v-model="form.precio"
                    label="Precio mensual *"
                    :error="errors.precio"
                    show-per-month
                    :show-calculations="true"
                    help-text="Precio mensual del alquiler"
                    required
                  />
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <PriceInput
                      v-model="form.fianza"
                      label="Fianza sugerida"
                      :error="errors.fianza"
                      help-text="Normalmente 1-2 meses"
                    />
                  </div>
                  <div class="col-md-6 mb-3">
                    <PriceInput
                      v-model="form.gastos_comunidad"
                      label="Gastos comunidad"
                      :error="errors.gastos_comunidad"
                      help-text="Gastos mensuales de comunidad"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Información adicional -->
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-clipboard-list me-2"></i>Información Adicional
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Características y equipamiento</label>
                  <textarea
                    v-model="form.caracteristicas"
                    class="form-control"
                    :class="{ 'is-invalid': errors.caracteristicas }"
                    rows="4"
                    placeholder="Describe las características especiales, equipamiento, reformas recientes, etc."
                  ></textarea>
                  <div v-if="errors.caracteristicas" class="invalid-feedback">
                    {{ errors.caracteristicas }}
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">Notas internas</label>
                  <textarea
                    v-model="form.notas_internas"
                    class="form-control"
                    rows="3"
                    placeholder="Notas para uso interno, no visibles para clientes"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <router-link to="/pisos" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>Cancelar
              </router-link>
              <button
                type="button"
                class="btn btn-outline-primary"
                @click="saveDraft"
                :disabled="isSubmitting"
              >
                <i class="fas fa-save me-1"></i>Guardar borrador
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="isSubmitting || !isFormValid"
              >
                <i class="fas fa-check me-1"></i>
                {{ isSubmitting ? 'Guardando...' : (isEdit ? 'Actualizar' : 'Crear piso') }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { pisosService } from '@/services/pisos'
import PriceInput from '@/components/common/PriceInput.vue'
import moment from 'moment'

export default {
  name: 'PisoForm',
  components: {
    PriceInput
  },
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()

    const isSubmitting = ref(false)
    const errors = ref({})

    const form = ref({
      direccion: '',
      piso: '',
      codigo_postal: '',
      ciudad: 'Barcelona',
      provincia: 'Barcelona',
      superficie: null,
      habitaciones: '',
      banos: '',
      planta: '',
      precio: null,
      fianza: null,
      gastos_comunidad: null,
      estado: 'disponible',
      fecha_disponibilidad: null,
      tiene_ascensor: false,
      tiene_balcon: false,
      amueblado: false,
      caracteristicas: '',
      notas_internas: ''
    })

    const today = computed(() => moment().format('YYYY-MM-DD'))
    const isEdit = computed(() => !!props.id)

    const isFormValid = computed(() => {
      return form.value.direccion &&
             form.value.piso &&
             form.value.superficie &&
             form.value.habitaciones &&
             form.value.banos &&
             form.value.precio &&
             Object.keys(errors.value).length === 0
    })

    const validateForm = () => {
      errors.value = {}

      // Validaciones básicas
      if (!form.value.direccion?.trim()) {
        errors.value.direccion = 'La dirección es obligatoria'
      }

      if (!form.value.piso?.trim()) {
        errors.value.piso = 'El piso/puerta es obligatorio'
      }

      if (!form.value.superficie || form.value.superficie <= 0) {
        errors.value.superficie = 'La superficie debe ser mayor a 0'
      }

      if (!form.value.habitaciones) {
        errors.value.habitaciones = 'Debe seleccionar el número de habitaciones'
      }

      if (!form.value.banos) {
        errors.value.banos = 'Debe seleccionar el número de baños'
      }

      if (!form.value.precio || form.value.precio <= 0) {
        errors.value.precio = 'El precio debe ser mayor a 0'
      }

      // Validar código postal si se proporciona
      if (form.value.codigo_postal && !/^\d{5}$/.test(form.value.codigo_postal)) {
        errors.value.codigo_postal = 'El código postal debe tener 5 dígitos'
      }

      // Validar fecha de disponibilidad
      if (form.value.fecha_disponibilidad && moment(form.value.fecha_disponibilidad).isBefore(moment(), 'day')) {
        errors.value.fecha_disponibilidad = 'La fecha de disponibilidad no puede ser anterior a hoy'
      }

      return Object.keys(errors.value).length === 0
    }

    const saveDraft = async () => {
      const originalEstado = form.value.estado
      form.value.estado = 'borrador'

      const success = await handleSubmit()

      if (!success) {
        form.value.estado = originalEstado
      }
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return false
      }

      isSubmitting.value = true

      try {
        // Preparar datos para envío
        const submitData = {
          ...form.value,
          superficie: parseFloat(form.value.superficie),
          habitaciones: parseInt(form.value.habitaciones),
          banos: parseFloat(form.value.banos),
          precio: parseFloat(form.value.precio),
          fianza: form.value.fianza ? parseFloat(form.value.fianza) : null,
          gastos_comunidad: form.value.gastos_comunidad ? parseFloat(form.value.gastos_comunidad) : null
        }

        if (isEdit.value) {
          await pisosService.update(props.id, submitData)
          toast.success('Piso actualizado correctamente')
        } else {
          await pisosService.create(submitData)
          toast.success('Piso creado correctamente')
        }

        router.push('/pisos')
        return true
      } catch (error) {
        console.error('Error al guardar piso:', error)
        const message = error.response?.data?.message || 'Error al guardar el piso'
        toast.error(message)

        // Manejar errores de validación del servidor
        if (error.response?.data?.errors) {
          errors.value = error.response.data.errors
        }

        return false
      } finally {
        isSubmitting.value = false
      }
    }

    // Cargar datos si es edición
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await pisosService.getById(props.id)
          const piso = response.data

          // Mapear los datos del servidor al formulario
          form.value = {
            direccion: piso.direccion || '',
            piso: piso.piso || '',
            codigo_postal: piso.codigo_postal || '',
            ciudad: piso.ciudad || 'Barcelona',
            provincia: piso.provincia || 'Barcelona',
            superficie: piso.superficie || null,
            habitaciones: piso.habitaciones?.toString() || '',
            banos: piso.banos?.toString() || '',
            planta: piso.planta || '',
            precio: piso.precio || null,
            fianza: piso.fianza || null,
            gastos_comunidad: piso.gastos_comunidad || null,
            estado: piso.estado || 'disponible',
            fecha_disponibilidad: piso.fecha_disponibilidad || null,
            tiene_ascensor: piso.tiene_ascensor || false,
            tiene_balcon: piso.tiene_balcon || false,
            amueblado: piso.amueblado || false,
            caracteristicas: piso.caracteristicas || '',
            notas_internas: piso.notas_internas || ''
          }
        } catch (error) {
          console.error('Error al cargar piso:', error)
          toast.error('Error al cargar los datos del piso')
          router.push('/pisos')
        }
      }
    })

    return {
      form,
      errors,
      today,
      isEdit,
      isSubmitting,
      isFormValid,
      saveDraft,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.piso-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.piso-form .card-title {
  color: #495057;
  font-weight: 600;
}

.piso-form .form-check {
  padding-left: 1.5rem;
}

.piso-form .form-check-input {
  margin-left: -1.5rem;
}

.piso-form .form-check-label {
  font-weight: 500;
  color: #495057;
}

.piso-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.piso-form .gap-2 {
  gap: 0.5rem !important;
}

.piso-form .form-control:focus,
.piso-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.piso-form .is-invalid {
  border-color: #dc3545;
}

.piso-form .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.piso-form .form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.piso-form .alert {
  border: none;
  border-radius: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .piso-form .card-body {
    padding: 1rem;
  }

  .piso-form .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .piso-form .d-flex {
    flex-direction: column;
  }
}
</style>
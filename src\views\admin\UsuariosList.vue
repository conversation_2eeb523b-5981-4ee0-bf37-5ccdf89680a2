<template>
  <div class="usuarios-list">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Gestión de Usuarios</h1>
              <p class="text-muted mb-0">Administra los usuarios del sistema</p>
            </div>
            <router-link to="/admin/usuarios/nuevo" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>Nuevo Usuario
            </router-link>
          </div>
        </div>
      </div>

      <!-- Filtros y búsqueda -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-md-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input 
                      v-model="searchTerm" 
                      type="text" 
                      class="form-control" 
                      placeholder="Buscar usuarios..."
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <select v-model="filterRol" class="form-select">
                    <option value="">Todos los roles</option>
                    <option value="admin">Administrador</option>
                    <option value="manager">Gestor</option>
                    <option value="employee">Empleado</option>
                    <option value="viewer">Solo lectura</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <select v-model="filterEstado" class="form-select">
                    <option value="">Todos los estados</option>
                    <option value="activo">Activo</option>
                    <option value="inactivo">Inactivo</option>
                    <option value="suspendido">Suspendido</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <button 
                    class="btn btn-outline-secondary w-100"
                    @click="clearFilters"
                  >
                    <i class="fas fa-times me-1"></i>Limpiar
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Lista de usuarios -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="fas fa-users me-2"></i>
                Usuarios ({{ filteredUsuarios.length }})
              </h5>
              <div class="btn-group btn-group-sm">
                <button 
                  class="btn btn-outline-primary"
                  @click="loadUsuarios"
                  :disabled="isLoading"
                >
                  <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': isLoading }"></i>
                  Actualizar
                </button>
                <button class="btn btn-outline-success">
                  <i class="fas fa-download me-1"></i>Exportar
                </button>
              </div>
            </div>
            <div class="card-body p-0">
              <!-- Loading state -->
              <div v-if="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-3 text-muted">Cargando usuarios...</p>
              </div>

              <!-- Empty state -->
              <div v-else-if="filteredUsuarios.length === 0" class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay usuarios</h5>
                <p class="text-muted">
                  {{ searchTerm || filterRol || filterEstado ? 'No se encontraron usuarios con los filtros aplicados' : 'Aún no hay usuarios registrados' }}
                </p>
                <router-link v-if="!searchTerm && !filterRol && !filterEstado" to="/admin/usuarios/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Crear primer usuario
                </router-link>
              </div>

              <!-- Tabla de usuarios -->
              <div v-else class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Usuario</th>
                      <th>Email</th>
                      <th>Rol</th>
                      <th>Estado</th>
                      <th>Último acceso</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="usuario in paginatedUsuarios" :key="usuario.id">
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar me-3">
                            <img 
                              v-if="usuario.avatar" 
                              :src="usuario.avatar" 
                              :alt="usuario.nombre"
                              class="rounded-circle"
                              width="40"
                              height="40"
                            >
                            <div 
                              v-else 
                              class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center"
                            >
                              {{ getInitials(usuario.nombre, usuario.apellidos) }}
                            </div>
                          </div>
                          <div>
                            <div class="fw-bold">{{ usuario.nombre }} {{ usuario.apellidos }}</div>
                            <small class="text-muted">@{{ usuario.username }}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div>{{ usuario.email }}</div>
                        <small v-if="usuario.telefono" class="text-muted">{{ usuario.telefono }}</small>
                      </td>
                      <td>
                        <span :class="getRolClass(usuario.rol)">
                          {{ getRolText(usuario.rol) }}
                        </span>
                      </td>
                      <td>
                        <span :class="getEstadoClass(usuario.estado)">
                          {{ getEstadoText(usuario.estado) }}
                        </span>
                      </td>
                      <td>
                        <div v-if="usuario.ultimo_acceso">
                          {{ formatDate(usuario.ultimo_acceso) }}
                        </div>
                        <small v-else class="text-muted">Nunca</small>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <router-link 
                            :to="`/admin/usuarios/${usuario.id}/editar`"
                            class="btn btn-outline-primary"
                            title="Editar usuario"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button 
                            class="btn btn-outline-warning"
                            @click="toggleEstado(usuario)"
                            :title="usuario.estado === 'activo' ? 'Desactivar' : 'Activar'"
                          >
                            <i :class="usuario.estado === 'activo' ? 'fas fa-pause' : 'fas fa-play'"></i>
                          </button>
                          <button 
                            class="btn btn-outline-danger"
                            @click="confirmDelete(usuario)"
                            title="Eliminar usuario"
                            :disabled="usuario.id === currentUserId"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Paginación -->
              <div v-if="totalPages > 1" class="card-footer">
                <nav aria-label="Paginación de usuarios">
                  <ul class="pagination pagination-sm justify-content-center mb-0">
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="goToPage(currentPage - 1)">
                        <i class="fas fa-chevron-left"></i>
                      </button>
                    </li>
                    
                    <li 
                      v-for="page in visiblePages" 
                      :key="page"
                      class="page-item" 
                      :class="{ active: page === currentPage }"
                    >
                      <button class="page-link" @click="goToPage(page)">
                        {{ page }}
                      </button>
                    </li>
                    
                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="goToPage(currentPage + 1)">
                        <i class="fas fa-chevron-right"></i>
                      </button>
                    </li>
                  </ul>
                </nav>
                
                <div class="text-center mt-2">
                  <small class="text-muted">
                    Mostrando {{ startIndex + 1 }} - {{ Math.min(endIndex, filteredUsuarios.length) }} 
                    de {{ filteredUsuarios.length }} usuarios
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmación -->
    <div v-if="showDeleteModal" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmar eliminación</h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="showDeleteModal = false"
            ></button>
          </div>
          <div class="modal-body">
            <p>¿Estás seguro de que quieres eliminar al usuario <strong>{{ userToDelete?.nombre }} {{ userToDelete?.apellidos }}</strong>?</p>
            <p class="text-danger">
              <i class="fas fa-exclamation-triangle me-1"></i>
              Esta acción no se puede deshacer.
            </p>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="showDeleteModal = false"
            >
              Cancelar
            </button>
            <button 
              type="button" 
              class="btn btn-danger"
              @click="deleteUser"
              :disabled="isDeleting"
            >
              <i class="fas fa-trash me-1"></i>
              {{ isDeleting ? 'Eliminando...' : 'Eliminar' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { usuariosService } from '@/services/usuarios'
import moment from 'moment'

export default {
  name: 'UsuariosList',
  setup() {
    const toast = useToast()
    
    const usuarios = ref([])
    const isLoading = ref(false)
    const isDeleting = ref(false)
    const searchTerm = ref('')
    const filterRol = ref('')
    const filterEstado = ref('')
    const currentPage = ref(1)
    const itemsPerPage = 10
    const showDeleteModal = ref(false)
    const userToDelete = ref(null)
    const currentUserId = ref(1) // ID del usuario actual
    
    const filteredUsuarios = computed(() => {
      let filtered = usuarios.value

      if (searchTerm.value) {
        const term = searchTerm.value.toLowerCase()
        filtered = filtered.filter(usuario =>
          usuario.nombre.toLowerCase().includes(term) ||
          usuario.apellidos.toLowerCase().includes(term) ||
          usuario.email.toLowerCase().includes(term) ||
          usuario.username.toLowerCase().includes(term)
        )
      }

      if (filterRol.value) {
        filtered = filtered.filter(usuario => usuario.rol === filterRol.value)
      }

      if (filterEstado.value) {
        filtered = filtered.filter(usuario => usuario.estado === filterEstado.value)
      }

      return filtered
    })

    const totalPages = computed(() => Math.ceil(filteredUsuarios.value.length / itemsPerPage))
    const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)
    const endIndex = computed(() => startIndex.value + itemsPerPage)

    const paginatedUsuarios = computed(() => {
      return filteredUsuarios.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    })

    const getInitials = (nombre, apellidos) => {
      const n = nombre ? nombre.charAt(0).toUpperCase() : ''
      const a = apellidos ? apellidos.charAt(0).toUpperCase() : ''
      return n + a
    }

    const getRolClass = (rol) => {
      const classes = {
        'admin': 'badge bg-danger',
        'manager': 'badge bg-warning text-dark',
        'employee': 'badge bg-primary',
        'viewer': 'badge bg-secondary'
      }
      return classes[rol] || 'badge bg-secondary'
    }

    const getRolText = (rol) => {
      const texts = {
        'admin': 'Administrador',
        'manager': 'Gestor',
        'employee': 'Empleado',
        'viewer': 'Solo lectura'
      }
      return texts[rol] || rol
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'activo': 'badge bg-success',
        'inactivo': 'badge bg-secondary',
        'suspendido': 'badge bg-danger'
      }
      return classes[estado] || 'badge bg-secondary'
    }

    const getEstadoText = (estado) => {
      const texts = {
        'activo': 'Activo',
        'inactivo': 'Inactivo',
        'suspendido': 'Suspendido'
      }
      return texts[estado] || estado
    }

    const formatDate = (date) => {
      return moment(date).format('DD/MM/YYYY HH:mm')
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const clearFilters = () => {
      searchTerm.value = ''
      filterRol.value = ''
      filterEstado.value = ''
      currentPage.value = 1
    }

    const loadUsuarios = async () => {
      isLoading.value = true
      try {
        const response = await usuariosService.getAll()
        usuarios.value = response.data
      } catch (error) {
        console.error('Error al cargar usuarios:', error)
        toast.error('Error al cargar la lista de usuarios')
        usuarios.value = [] // Limpiar la lista en caso de error
      } finally {
        isLoading.value = false
      }
    }

    const toggleEstado = async (usuario) => {
      try {
        const nuevoEstado = usuario.estado === 'activo' ? 'inactivo' : 'activo'
        await usuariosService.updateEstado(usuario.id, nuevoEstado)

        usuario.estado = nuevoEstado
        toast.success(`Usuario ${nuevoEstado === 'activo' ? 'activado' : 'desactivado'} correctamente`)
      } catch (error) {
        console.error('Error al cambiar estado:', error)
        toast.error('Error al cambiar el estado del usuario')
      }
    }

    const confirmDelete = (usuario) => {
      userToDelete.value = usuario
      showDeleteModal.value = true
    }

    const deleteUser = async () => {
      if (!userToDelete.value) return

      isDeleting.value = true
      try {
        await usuariosService.delete(userToDelete.value.id)

        usuarios.value = usuarios.value.filter(u => u.id !== userToDelete.value.id)
        showDeleteModal.value = false
        userToDelete.value = null
        toast.success('Usuario eliminado correctamente')
      } catch (error) {
        console.error('Error al eliminar usuario:', error)
        toast.error('Error al eliminar el usuario')
      } finally {
        isDeleting.value = false
      }
    }

    onMounted(() => {
      loadUsuarios()
    })

    return {
      usuarios,
      isLoading,
      isDeleting,
      searchTerm,
      filterRol,
      filterEstado,
      currentPage,
      itemsPerPage,
      showDeleteModal,
      userToDelete,
      currentUserId,
      filteredUsuarios,
      totalPages,
      startIndex,
      endIndex,
      paginatedUsuarios,
      visiblePages,
      getInitials,
      getRolClass,
      getRolText,
      getEstadoClass,
      getEstadoText,
      formatDate,
      goToPage,
      clearFilters,
      loadUsuarios,
      toggleEstado,
      confirmDelete,
      deleteUser
    }
  }
}
</script>

<style scoped>
.usuarios-list .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.usuarios-list .card-title {
  color: #495057;
  font-weight: 600;
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  background-color: #6c757d;
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.usuarios-list .table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.usuarios-list .table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.usuarios-list .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.usuarios-list .btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.usuarios-list .spinner-border {
  width: 2rem;
  height: 2rem;
}

.usuarios-list .modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.usuarios-list .pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.usuarios-list .input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .usuarios-list .card-body {
    padding: 0.5rem;
  }

  .usuarios-list .table-responsive {
    font-size: 0.8rem;
  }

  .usuarios-list .btn-group {
    flex-direction: column;
  }

  .usuarios-list .btn-group .btn {
    margin-bottom: 0.25rem;
  }

  .usuarios-list .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .usuarios-list .avatar-placeholder,
  .usuarios-list .avatar img {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}

/* Print styles */
@media print {
  .usuarios-list .btn,
  .usuarios-list .btn-group,
  .usuarios-list .pagination {
    display: none !important;
  }

  .usuarios-list .card {
    border: 1px solid #000;
  }
}
</style>

// Validaciones comunes

// Validar email
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validar teléfono español
export const isValidPhone = (phone) => {
  const phoneRegex = /^(\+34|0034|34)?[6789]\d{8}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// Validar DNI/NIE español
export const isValidDNI = (dni) => {
  const dniRegex = /^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i
  const nieRegex = /^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i
  
  if (!dniRegex.test(dni) && !nieRegex.test(dni)) {
    return false
  }
  
  const letters = 'TRWAGMYFPDXBNJZSQVHLCKE'
  let number = dni.substring(0, 8)
  
  if (nieRegex.test(dni)) {
    const firstChar = dni.charAt(0)
    if (firstChar === 'X') number = '0' + number
    else if (firstChar === 'Y') number = '1' + number
    else if (firstChar === 'Z') number = '2' + number
  }
  
  const expectedLetter = letters[parseInt(number) % 23]
  return expectedLetter === dni.charAt(8).toUpperCase()
}

// Validar código postal español
export const isValidPostalCode = (postalCode) => {
  const postalCodeRegex = /^[0-5]\d{4}$/
  return postalCodeRegex.test(postalCode)
}

// Validar IBAN
export const isValidIBAN = (iban) => {
  const ibanRegex = /^ES\d{2}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}$/
  return ibanRegex.test(iban.replace(/\s/g, ''))
}

// Validar que un campo no esté vacío
export const isRequired = (value) => {
  return value !== null && value !== undefined && value.toString().trim() !== ''
}

// Validar longitud mínima
export const minLength = (value, min) => {
  return value && value.toString().length >= min
}

// Validar longitud máxima
export const maxLength = (value, max) => {
  return !value || value.toString().length <= max
}

// Validar que sea un número
export const isNumber = (value) => {
  return !isNaN(value) && !isNaN(parseFloat(value))
}

// Validar que sea un número positivo
export const isPositiveNumber = (value) => {
  return isNumber(value) && parseFloat(value) > 0
}

// Validar que sea un entero
export const isInteger = (value) => {
  return Number.isInteger(Number(value))
}

// Validar rango de fechas
export const isValidDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return true
  return new Date(startDate) <= new Date(endDate)
}

// Validar que la fecha no sea pasada
export const isNotPastDate = (date) => {
  if (!date) return true
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return new Date(date) >= today
}

// Validar formato de archivo
export const isValidFileType = (file, allowedTypes) => {
  if (!file || !allowedTypes) return false
  
  const fileExtension = file.name.split('.').pop().toLowerCase()
  return allowedTypes.includes(fileExtension)
}

// Validar tamaño de archivo
export const isValidFileSize = (file, maxSizeInBytes) => {
  if (!file) return false
  return file.size <= maxSizeInBytes
}

// Validar URL
export const isValidURL = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Validar contraseña segura
export const isStrongPassword = (password) => {
  // Al menos 8 caracteres, una mayúscula, una minúscula, un número
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  return strongPasswordRegex.test(password)
}

// Validaciones compuestas para formularios
export const validateClienteForm = (cliente) => {
  const errors = {}
  
  if (!isRequired(cliente.nombre)) {
    errors.nombre = 'El nombre es obligatorio'
  }
  
  if (!isRequired(cliente.email)) {
    errors.email = 'El email es obligatorio'
  } else if (!isValidEmail(cliente.email)) {
    errors.email = 'El email no es válido'
  }
  
  if (cliente.telefono && !isValidPhone(cliente.telefono)) {
    errors.telefono = 'El teléfono no es válido'
  }
  
  if (cliente.dni && !isValidDNI(cliente.dni)) {
    errors.dni = 'El DNI/NIE no es válido'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

export const validateAlquilerForm = (alquiler) => {
  const errors = {}

  if (!isRequired(alquiler.cliente_id)) {
    errors.cliente_id = 'Debe seleccionar un cliente'
  }

  if (!isRequired(alquiler.valor)) {
    errors.valor = 'El valor del alquiler es obligatorio'
  } else if (!isPositiveNumber(alquiler.valor)) {
    errors.valor = 'El valor debe ser un número positivo'
  }

  if (!isRequired(alquiler.fecha_inicio)) {
    errors.fecha_inicio = 'La fecha de inicio es obligatoria'
  }

  if (!isValidDateRange(alquiler.fecha_inicio, alquiler.fecha_fin)) {
    errors.fecha_fin = 'La fecha de fin debe ser posterior a la fecha de inicio'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Validar formulario de piso
export const validatePisoForm = (piso) => {
  const errors = {}

  if (!isRequired(piso.direccion)) {
    errors.direccion = 'La dirección es obligatoria'
  }

  if (!isRequired(piso.piso)) {
    errors.piso = 'El piso/puerta es obligatorio'
  }

  if (!isPositiveNumber(piso.superficie)) {
    errors.superficie = 'La superficie debe ser un número positivo'
  }

  if (!isPositiveNumber(piso.habitaciones)) {
    errors.habitaciones = 'El número de habitaciones debe ser positivo'
  }

  if (!isPositiveNumber(piso.banos)) {
    errors.banos = 'El número de baños debe ser positivo'
  }

  if (!isPositiveNumber(piso.precio)) {
    errors.precio = 'El precio debe ser un número positivo'
  }

  if (piso.codigo_postal && !isValidPostalCode(piso.codigo_postal)) {
    errors.codigo_postal = 'El código postal no es válido'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Validar formulario de trastero
export const validateTrasteroForm = (trastero) => {
  const errors = {}

  if (!isRequired(trastero.numero)) {
    errors.numero = 'El número/identificador es obligatorio'
  }

  if (!isRequired(trastero.planta)) {
    errors.planta = 'La planta es obligatoria'
  }

  if (!isRequired(trastero.tamano)) {
    errors.tamano = 'El tamaño es obligatorio'
  }

  if (!isPositiveNumber(trastero.precio)) {
    errors.precio = 'El precio debe ser un número positivo'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Validar formulario de pago
export const validatePagoForm = (pago) => {
  const errors = {}

  if (!isPositiveNumber(pago.importe)) {
    errors.importe = 'El importe debe ser un número positivo'
  }

  if (!isRequired(pago.fecha_pago)) {
    errors.fecha_pago = 'La fecha de pago es obligatoria'
  }

  if (!isRequired(pago.metodo_pago)) {
    errors.metodo_pago = 'Debe seleccionar el método de pago'
  }

  if (pago.fecha_pago && isDateInFuture(pago.fecha_pago)) {
    errors.fecha_pago = 'La fecha de pago no puede ser futura'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Validar formulario de documento
export const validateDocumentoForm = (documento) => {
  const errors = {}

  if (!isRequired(documento.titulo)) {
    errors.titulo = 'El título es obligatorio'
  }

  if (!isRequired(documento.tipo)) {
    errors.tipo = 'Debe seleccionar el tipo de documento'
  }

  if (!documento.archivo && !documento.archivo_existente) {
    errors.archivo = 'Debe seleccionar un archivo'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Validaciones adicionales

// Validar que una fecha no sea futura
export const isDateInFuture = (date) => {
  const today = new Date()
  const inputDate = new Date(date)
  return inputDate > today
}

// Validar que una fecha no sea muy antigua (más de X años)
export const isDateTooOld = (date, maxYears = 10) => {
  const today = new Date()
  const inputDate = new Date(date)
  const yearsDiff = (today - inputDate) / (1000 * 60 * 60 * 24 * 365)
  return yearsDiff > maxYears
}

// Validar formato de archivo
export const isValidFileType = (fileName, allowedTypes) => {
  const fileExt = fileName.split('.').pop().toLowerCase()
  return allowedTypes.includes(fileExt)
}

// Validar tamaño de archivo
export const isValidFileSize = (fileSize, maxSize) => {
  return fileSize <= maxSize
}

// Validar precio con decimales
export const isValidPrice = (price) => {
  const priceRegex = /^\d+(\.\d{1,2})?$/
  return priceRegex.test(price.toString()) && parseFloat(price) > 0
}

// Validar número de habitaciones/baños
export const isValidRoomCount = (count) => {
  return Number.isInteger(parseFloat(count)) && parseFloat(count) > 0 && parseFloat(count) <= 20
}

// Validar superficie
export const isValidSurface = (surface) => {
  return parseFloat(surface) > 0 && parseFloat(surface) <= 10000
}

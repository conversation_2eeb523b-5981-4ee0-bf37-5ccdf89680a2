import api from './api'

export const gastosService = {
  // Obtener todos los gastos
  getAll(params = {}) {
    return api.get('/gastos-edificio', { params })
  },

  // Obtener gasto por ID
  getById(id) {
    return api.get(`/gastos-edificio/${id}`)
  },

  // Crear nuevo gasto
  create(data) {
    return api.post('/gastos-edificio', data)
  },

  // Actualizar gasto
  update(id, data) {
    return api.put(`/gastos-edificio/${id}`, data)
  },

  // Eliminar gasto
  delete(id) {
    return api.delete(`/gastos-edificio/${id}`)
  },

  // Obtener gastos pendientes
  getPendientes() {
    return api.get('/gastos-pendientes')
  },

  // Obtener gastos vencidos
  getVencidos() {
    return api.get('/gastos-vencidos')
  },

  // Obtener gastos por categoría
  getPorCategoria(categoria) {
    return api.get(`/gastos-categoria/${categoria}`)
  },

  // Marcar como pagado
  marcarPagado(id, data) {
    return api.patch(`/gastos-edificio/${id}/pagar`, data)
  },

  // Obtener gastos recurrentes
  getRecurrentes() {
    return api.get('/gastos-recurrentes')
  },

  // Generar gasto recurrente
  generarRecurrente(id) {
    return api.post(`/gastos-edificio/${id}/generar-recurrente`)
  }
}

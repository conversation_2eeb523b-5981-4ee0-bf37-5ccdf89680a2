<template>
  <div class="gasto-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Añadir' }} Gasto</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del gasto' : 'Registra un nuevo gasto del edificio' }}</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Concepto</label>
                    <input v-model="form.concepto" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Importe (€)</label>
                    <input v-model="form.importe" type="number" step="0.01" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Fecha</label>
                    <input v-model="form.fecha" type="date" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Categoría</label>
                    <select v-model="form.categoria" class="form-select" required>
                      <option value="">Selecciona una categoría</option>
                      <option value="mantenimiento">Mantenimiento</option>
                      <option value="limpieza">Limpieza</option>
                      <option value="seguridad">Seguridad</option>
                      <option value="suministros">Suministros</option>
                      <option value="seguros">Seguros</option>
                      <option value="administracion">Administración</option>
                      <option value="reparaciones">Reparaciones</option>
                      <option value="otros">Otros</option>
                    </select>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Estado</label>
                    <select v-model="form.estado" class="form-select" required>
                      <option value="pendiente">Pendiente</option>
                      <option value="pagado">Pagado</option>
                      <option value="vencido">Vencido</option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Fecha de vencimiento</label>
                    <input v-model="form.fechaVencimiento" type="date" class="form-control">
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input v-model="form.esRecurrente" class="form-check-input" type="checkbox" id="esRecurrente">
                      <label class="form-check-label" for="esRecurrente">
                        Es un gasto recurrente
                      </label>
                    </div>
                  </div>
                  <div class="col-md-6" v-if="form.esRecurrente">
                    <label class="form-label">Frecuencia</label>
                    <select v-model="form.frecuencia" class="form-select" :required="form.esRecurrente">
                      <option v-for="(value, key) in FRECUENCIAS_GASTO" :key="key" :value="value">
                        {{ key.charAt(0) + key.slice(1).toLowerCase() }}
                      </option>
                    </select>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Notas</label>
                    <textarea v-model="form.notas" class="form-control" rows="3"></textarea>
                  </div>
                </div>
                
                <div class="d-flex justify-content-end mt-4">
                  <router-link to="/gastos" class="btn btn-secondary me-2">Cancelar</router-link>
                  <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                    <i class="fas fa-save me-1"></i> {{ isSubmitting ? 'Guardando...' : 'Guardar' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { gastosService } from '@/services/gastos'
import { TIPOS_GASTO, FRECUENCIAS_GASTO } from '@/utils/constants'

export default {
  name: 'GastoForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const form = ref({
      concepto: '',
      importe: '',
      fecha: new Date().toISOString().substr(0, 10),
      categoria: '',
      estado: 'pendiente',
      fechaVencimiento: '',
      esRecurrente: false,
      frecuencia: FRECUENCIAS_GASTO.MENSUAL,
      notas: ''
    })
    
    const isEdit = computed(() => !!props.id)
    
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await gastosService.getById(props.id)
          form.value = response.data
        } catch (error) {
          toast.error('Error al cargar los datos del gasto')
        }
      }
    })
    
    const handleSubmit = async () => {
      isSubmitting.value = true
      
      try {
        if (isEdit.value) {
          await gastosService.update(props.id, form.value)
          toast.success('Gasto actualizado correctamente')
        } else {
          await gastosService.create(form.value)
          toast.success('Gasto creado correctamente')
        }
        router.push('/gastos')
      } catch (error) {
        const message = error.response?.data?.message || 'Error al guardar el gasto'
        toast.error(message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isEdit,
      isSubmitting,
      handleSubmit,
      TIPOS_GASTO,
      FRECUENCIAS_GASTO
    }
  }
}
</script>

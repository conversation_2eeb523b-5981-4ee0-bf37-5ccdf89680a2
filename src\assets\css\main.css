/* Variables CSS */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  
  --navbar-height: 70px;
  --sidebar-width: 250px;
  --sidebar-collapsed-width: 60px;
  
  --border-radius: 0.375rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Reset y base */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--light-color);
  color: var(--dark-color);
}

/* Utilidades */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;
}

.border-dashed {
  border-style: dashed !important;
}

/* Cards personalizadas */
.card {
  border: none;
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
}

.card-header {
  background-color: white;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

/* Botones personalizados */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-lg);
}

/* Tablas */
.table {
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table thead th {
  background-color: var(--light-color);
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: var(--dark-color);
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Formularios */
.form-control {
  border-radius: var(--border-radius);
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Estados de elementos */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-disponible {
  background-color: #d4edda;
  color: #155724;
}

.status-ocupado {
  background-color: #f8d7da;
  color: #721c24;
}

.status-pendiente {
  background-color: #fff3cd;
  color: #856404;
}

.status-pagado {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-vencido {
  background-color: #f5c6cb;
  color: #721c24;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Responsive */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .btn-group-vertical .btn {
    margin-bottom: 0.25rem;
  }
}

/* Drag and drop */
.drag-area {
  border: 2px dashed #ccc;
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.drag-area.drag-over {
  border-color: var(--primary-color);
  background-color: rgba(0, 123, 255, 0.05);
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Navbar */
.navbar {
  height: var(--navbar-height);
  box-shadow: var(--box-shadow);
}

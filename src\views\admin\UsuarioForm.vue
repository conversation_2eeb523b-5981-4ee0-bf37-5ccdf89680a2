<template>
  <div class="usuario-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Nuevo' }} Usuario</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del usuario' : 'Crea un nuevo usuario del sistema' }}</p>
        </div>
      </div>
      
      <form @submit.prevent="handleSubmit">
        <div class="row">
          <!-- Información personal -->
          <div class="col-lg-8">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-user me-2"></i>Información Personal
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Nombre *</label>
                    <input 
                      v-model="form.nombre" 
                      type="text" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.nombre }"
                      required
                    >
                    <div v-if="errors.nombre" class="invalid-feedback">
                      {{ errors.nombre }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Apellidos *</label>
                    <input 
                      v-model="form.apellidos" 
                      type="text" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.apellidos }"
                      required
                    >
                    <div v-if="errors.apellidos" class="invalid-feedback">
                      {{ errors.apellidos }}
                    </div>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Email *</label>
                    <input 
                      v-model="form.email" 
                      type="email" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.email }"
                      required
                    >
                    <div v-if="errors.email" class="invalid-feedback">
                      {{ errors.email }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Teléfono</label>
                    <input 
                      v-model="form.telefono" 
                      type="tel" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.telefono }"
                      placeholder="93 123 45 67"
                    >
                    <div v-if="errors.telefono" class="invalid-feedback">
                      {{ errors.telefono }}
                    </div>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">DNI/NIE</label>
                    <input 
                      v-model="form.dni" 
                      type="text" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.dni }"
                      placeholder="12345678A"
                    >
                    <div v-if="errors.dni" class="invalid-feedback">
                      {{ errors.dni }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Fecha de nacimiento</label>
                    <input 
                      v-model="form.fecha_nacimiento" 
                      type="date" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.fecha_nacimiento }"
                      :max="maxBirthDate"
                    >
                    <div v-if="errors.fecha_nacimiento" class="invalid-feedback">
                      {{ errors.fecha_nacimiento }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Credenciales de acceso -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-key me-2"></i>Credenciales de Acceso
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Nombre de usuario *</label>
                    <input 
                      v-model="form.username" 
                      type="text" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.username }"
                      :readonly="isEdit"
                      required
                    >
                    <small v-if="isEdit" class="form-text text-muted">
                      El nombre de usuario no se puede modificar
                    </small>
                    <div v-if="errors.username" class="invalid-feedback">
                      {{ errors.username }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">{{ isEdit ? 'Nueva contraseña' : 'Contraseña *' }}</label>
                    <div class="input-group">
                      <input 
                        v-model="form.password" 
                        :type="showPassword ? 'text' : 'password'" 
                        class="form-control" 
                        :class="{ 'is-invalid': errors.password }"
                        :required="!isEdit"
                        :placeholder="isEdit ? 'Dejar vacío para mantener la actual' : ''"
                      >
                      <button 
                        type="button" 
                        class="btn btn-outline-secondary"
                        @click="showPassword = !showPassword"
                      >
                        <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                      </button>
                    </div>
                    <div v-if="errors.password" class="invalid-feedback">
                      {{ errors.password }}
                    </div>
                  </div>
                </div>
                
                <div v-if="form.password" class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Confirmar contraseña *</label>
                    <input 
                      v-model="form.password_confirmation" 
                      :type="showPassword ? 'text' : 'password'" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.password_confirmation }"
                      required
                    >
                    <div v-if="errors.password_confirmation" class="invalid-feedback">
                      {{ errors.password_confirmation }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="password-strength mt-4">
                      <small class="text-muted">Fortaleza de la contraseña:</small>
                      <div class="progress mt-1" style="height: 5px;">
                        <div 
                          class="progress-bar" 
                          :class="passwordStrengthClass"
                          :style="{ width: passwordStrengthPercent + '%' }"
                        ></div>
                      </div>
                      <small :class="passwordStrengthClass">{{ passwordStrengthText }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Configuración de usuario -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-cog me-2"></i>Configuración
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Rol del usuario *</label>
                  <select 
                    v-model="form.rol" 
                    class="form-select" 
                    :class="{ 'is-invalid': errors.rol }"
                    required
                  >
                    <option value="">Selecciona un rol...</option>
                    <option value="admin">Administrador</option>
                    <option value="manager">Gestor</option>
                    <option value="employee">Empleado</option>
                    <option value="viewer">Solo lectura</option>
                  </select>
                  <small class="form-text text-muted">
                    Define los permisos del usuario en el sistema
                  </small>
                  <div v-if="errors.rol" class="invalid-feedback">
                    {{ errors.rol }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Estado</label>
                  <select 
                    v-model="form.estado" 
                    class="form-select" 
                    :class="{ 'is-invalid': errors.estado }"
                  >
                    <option value="activo">Activo</option>
                    <option value="inactivo">Inactivo</option>
                    <option value="suspendido">Suspendido</option>
                  </select>
                  <div v-if="errors.estado" class="invalid-feedback">
                    {{ errors.estado }}
                  </div>
                </div>
                
                <div class="mb-3">
                  <h6>Permisos especiales:</h6>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.puede_crear_usuarios" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="puede_crear_usuarios"
                    >
                    <label class="form-check-label" for="puede_crear_usuarios">
                      Crear usuarios
                    </label>
                  </div>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.puede_eliminar_datos" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="puede_eliminar_datos"
                    >
                    <label class="form-check-label" for="puede_eliminar_datos">
                      Eliminar datos
                    </label>
                  </div>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.puede_exportar_datos" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="puede_exportar_datos"
                    >
                    <label class="form-check-label" for="puede_exportar_datos">
                      Exportar datos
                    </label>
                  </div>
                  
                  <div class="form-check">
                    <input 
                      v-model="form.acceso_configuracion" 
                      class="form-check-input" 
                      type="checkbox" 
                      id="acceso_configuracion"
                    >
                    <label class="form-check-label" for="acceso_configuracion">
                      Acceso a configuración
                    </label>
                  </div>
                </div>
                
                <div class="form-check">
                  <input 
                    v-model="form.requiere_cambio_password" 
                    class="form-check-input" 
                    type="checkbox" 
                    id="requiere_cambio_password"
                  >
                  <label class="form-check-label" for="requiere_cambio_password">
                    Requerir cambio de contraseña en el próximo acceso
                  </label>
                </div>
              </div>
            </div>
            
            <!-- Información adicional -->
            <div v-if="isEdit" class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-info-circle me-2"></i>Información Adicional
                </h5>
              </div>
              <div class="card-body">
                <div class="info-item">
                  <label>Último acceso:</label>
                  <div>{{ formatDate(usuario?.ultimo_acceso) || 'Nunca' }}</div>
                </div>
                <div class="info-item">
                  <label>Fecha de creación:</label>
                  <div>{{ formatDate(usuario?.created_at) }}</div>
                </div>
                <div class="info-item">
                  <label>Intentos de acceso fallidos:</label>
                  <div>{{ usuario?.intentos_fallidos || 0 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <router-link to="/admin/usuarios" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>Cancelar
              </router-link>
              <button 
                type="submit" 
                class="btn btn-primary"
                :disabled="isSubmitting || !isFormValid"
              >
                <i class="fas fa-save me-1"></i>
                {{ isSubmitting ? 'Guardando...' : (isEdit ? 'Actualizar usuario' : 'Crear usuario') }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'vue-toastification'
import moment from 'moment'

export default {
  name: 'UsuarioForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const showPassword = ref(false)
    const errors = ref({})
    const usuario = ref(null)
    
    const form = ref({
      nombre: '',
      apellidos: '',
      email: '',
      telefono: '',
      dni: '',
      fecha_nacimiento: null,
      username: '',
      password: '',
      password_confirmation: '',
      rol: '',
      estado: 'activo',
      puede_crear_usuarios: false,
      puede_eliminar_datos: false,
      puede_exportar_datos: false,
      acceso_configuracion: false,
      requiere_cambio_password: false
    })
    
    const isEdit = computed(() => !!props.id)
    const maxBirthDate = computed(() => moment().subtract(16, 'years').format('YYYY-MM-DD'))
    
    const isFormValid = computed(() => {
      return form.value.nombre &&
             form.value.apellidos &&
             form.value.email &&
             form.value.username &&
             form.value.rol &&
             (isEdit.value || form.value.password) &&
             Object.keys(errors.value).length === 0
    })

    const passwordStrengthPercent = computed(() => {
      if (!form.value.password) return 0

      let strength = 0
      const password = form.value.password

      if (password.length >= 8) strength += 25
      if (/[a-z]/.test(password)) strength += 25
      if (/[A-Z]/.test(password)) strength += 25
      if (/[0-9]/.test(password)) strength += 25
      if (/[^A-Za-z0-9]/.test(password)) strength += 25

      return Math.min(strength, 100)
    })

    const passwordStrengthClass = computed(() => {
      const percent = passwordStrengthPercent.value
      if (percent < 25) return 'text-danger'
      if (percent < 50) return 'text-warning'
      if (percent < 75) return 'text-info'
      return 'text-success'
    })

    const passwordStrengthText = computed(() => {
      const percent = passwordStrengthPercent.value
      if (percent < 25) return 'Muy débil'
      if (percent < 50) return 'Débil'
      if (percent < 75) return 'Media'
      return 'Fuerte'
    })

    const formatDate = (date) => {
      return date ? moment(date).format('DD/MM/YYYY HH:mm') : ''
    }

    const validateForm = () => {
      errors.value = {}

      if (!form.value.nombre?.trim()) {
        errors.value.nombre = 'El nombre es obligatorio'
      }

      if (!form.value.apellidos?.trim()) {
        errors.value.apellidos = 'Los apellidos son obligatorios'
      }

      if (!form.value.email?.trim()) {
        errors.value.email = 'El email es obligatorio'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
        errors.value.email = 'El email no tiene un formato válido'
      }

      if (!form.value.username?.trim()) {
        errors.value.username = 'El nombre de usuario es obligatorio'
      } else if (form.value.username.length < 3) {
        errors.value.username = 'El nombre de usuario debe tener al menos 3 caracteres'
      }

      if (!isEdit.value && !form.value.password) {
        errors.value.password = 'La contraseña es obligatoria'
      } else if (form.value.password && form.value.password.length < 8) {
        errors.value.password = 'La contraseña debe tener al menos 8 caracteres'
      }

      if (form.value.password && form.value.password !== form.value.password_confirmation) {
        errors.value.password_confirmation = 'Las contraseñas no coinciden'
      }

      if (!form.value.rol) {
        errors.value.rol = 'Debe seleccionar un rol'
      }

      if (form.value.telefono && !/^(\+34|0034|34)?[6789]\d{8}$/.test(form.value.telefono.replace(/\s/g, ''))) {
        errors.value.telefono = 'El teléfono no tiene un formato válido'
      }

      if (form.value.dni && !/^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i.test(form.value.dni)) {
        errors.value.dni = 'El DNI no tiene un formato válido'
      }

      return Object.keys(errors.value).length === 0
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isSubmitting.value = true

      try {
        const submitData = { ...form.value }

        // No enviar password_confirmation
        delete submitData.password_confirmation

        // Si es edición y no hay password, no enviarlo
        if (isEdit.value && !submitData.password) {
          delete submitData.password
        }

        if (isEdit.value) {
          // await usuariosService.update(props.id, submitData)
          toast.success('Usuario actualizado correctamente')
        } else {
          // await usuariosService.create(submitData)
          toast.success('Usuario creado correctamente')
        }

        router.push('/admin/usuarios')
      } catch (error) {
        console.error('Error al guardar usuario:', error)
        const message = error.response?.data?.message || 'Error al guardar el usuario'
        toast.error(message)

        if (error.response?.data?.errors) {
          errors.value = error.response.data.errors
        }
      } finally {
        isSubmitting.value = false
      }
    }

    // Cargar datos si es edición
    onMounted(async () => {
      if (isEdit.value) {
        try {
          // const response = await usuariosService.getById(props.id)
          // usuario.value = response.data

          // Simular datos de usuario
          usuario.value = {
            id: props.id,
            nombre: 'Juan',
            apellidos: 'Pérez García',
            email: '<EMAIL>',
            telefono: '93 123 45 67',
            dni: '12345678A',
            username: 'jperez',
            rol: 'manager',
            estado: 'activo',
            ultimo_acceso: '2024-01-15T10:30:00Z',
            created_at: '2023-06-01T09:00:00Z',
            intentos_fallidos: 0
          }

          // Mapear datos al formulario
          Object.keys(form.value).forEach(key => {
            if (usuario.value.hasOwnProperty(key) && key !== 'password' && key !== 'password_confirmation') {
              form.value[key] = usuario.value[key]
            }
          })

        } catch (error) {
          console.error('Error al cargar usuario:', error)
          toast.error('Error al cargar los datos del usuario')
          router.push('/admin/usuarios')
        }
      }
    })

    return {
      form,
      errors,
      isEdit,
      isSubmitting,
      showPassword,
      usuario,
      maxBirthDate,
      isFormValid,
      passwordStrengthPercent,
      passwordStrengthClass,
      passwordStrengthText,
      formatDate,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.usuario-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.usuario-form .card-title {
  color: #495057;
  font-weight: 600;
}

.usuario-form .form-check {
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.usuario-form .form-check-input {
  margin-left: -1.5rem;
}

.usuario-form .form-check-label {
  font-weight: 500;
  color: #495057;
}

.usuario-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.usuario-form .gap-2 {
  gap: 0.5rem !important;
}

.usuario-form .form-control:focus,
.usuario-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.usuario-form .is-invalid {
  border-color: #dc3545;
}

.usuario-form .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.usuario-form .form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.usuario-form h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.info-item {
  margin-bottom: 1rem;
}

.info-item label {
  display: block;
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.password-strength .progress {
  height: 5px;
}

.password-strength .progress-bar {
  transition: width 0.3s ease;
}

.password-strength .text-danger .progress-bar {
  background-color: #dc3545;
}

.password-strength .text-warning .progress-bar {
  background-color: #ffc107;
}

.password-strength .text-info .progress-bar {
  background-color: #17a2b8;
}

.password-strength .text-success .progress-bar {
  background-color: #28a745;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .usuario-form .card-body {
    padding: 1rem;
  }

  .usuario-form .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .usuario-form .d-flex {
    flex-direction: column;
  }

  .usuario-form .input-group .btn {
    width: auto;
    margin-bottom: 0;
  }
}
</style>

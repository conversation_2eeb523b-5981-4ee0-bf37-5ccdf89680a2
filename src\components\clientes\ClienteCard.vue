<template>
  <div class="cliente-card">
    <div class="card h-100" :class="{ 'border-primary': isSelected }">
      <div class="card-body">
        <!-- Header con avatar y estado -->
        <div class="d-flex align-items-center mb-3">
          <div class="avatar me-3">
            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
              {{ getInitials(cliente.nombre, cliente.apellidos) }}
            </div>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-0 cliente-name">{{ cliente.nombre }} {{ cliente.apellidos }}</h6>
            <small class="text-muted">{{ cliente.dni }}</small>
          </div>
          <span :class="getEstadoClass(cliente.estado)" class="badge">
            {{ getEstadoText(cliente.estado) }}
          </span>
        </div>
        
        <!-- Información de contacto -->
        <div class="contact-info mb-3">
          <div class="contact-item">
            <i class="fas fa-envelope text-muted me-2"></i>
            <span class="contact-text">{{ cliente.email }}</span>
          </div>
          <div class="contact-item">
            <i class="fas fa-phone text-muted me-2"></i>
            <span class="contact-text">{{ cliente.telefono }}</span>
          </div>
          <div v-if="cliente.direccion" class="contact-item">
            <i class="fas fa-map-marker-alt text-muted me-2"></i>
            <span class="contact-text">{{ truncateText(cliente.direccion, 30) }}</span>
          </div>
        </div>

        <!-- Estadísticas -->
        <div class="stats-row mb-3">
          <div class="stat-item">
            <i class="fas fa-file-contract text-primary me-1"></i>
            <span class="stat-value">{{ cliente.alquileres_count || 0 }}</span>
            <span class="stat-label">alquiler(es)</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-calendar text-info me-1"></i>
            <span class="stat-value">{{ formatFecha(cliente.created_at) }}</span>
          </div>
        </div>

        <!-- Notas (si existen) -->
        <div v-if="cliente.notas && showNotes" class="notes mb-3">
          <div class="notes-content">
            <i class="fas fa-sticky-note text-warning me-2"></i>
            <span class="notes-text">{{ truncateText(cliente.notas, 50) }}</span>
          </div>
        </div>

        <!-- Acciones -->
        <div class="actions-row">
          <div class="btn-group w-100" role="group">
            <button
              @click="$emit('view', cliente)"
              class="btn btn-sm btn-outline-primary"
              title="Ver detalle"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button
              @click="$emit('edit', cliente)"
              class="btn btn-sm btn-outline-warning"
              title="Editar"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              v-if="showQuickActions"
              @click="toggleQuickActions"
              class="btn btn-sm btn-outline-info"
              title="Acciones rápidas"
            >
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <button
              @click="$emit('delete', cliente)"
              class="btn btn-sm btn-outline-danger"
              title="Eliminar"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Acciones rápidas expandidas -->
        <div v-if="showQuickActions && quickActionsExpanded" class="quick-actions mt-2">
          <div class="d-flex gap-1">
            <button
              @click="callClient"
              class="btn btn-sm btn-success flex-fill"
              :disabled="!cliente.telefono"
              title="Llamar"
            >
              <i class="fas fa-phone"></i>
            </button>
            <button
              @click="emailClient"
              class="btn btn-sm btn-info flex-fill"
              :disabled="!cliente.email"
              title="Email"
            >
              <i class="fas fa-envelope"></i>
            </button>
            <button
              @click="$emit('new-rental', cliente)"
              class="btn btn-sm btn-primary flex-fill"
              title="Nuevo alquiler"
            >
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Footer con última actualización -->
      <div v-if="showFooter" class="card-footer text-muted small">
        <i class="fas fa-clock me-1"></i>
        Actualizado {{ formatFechaRelativa(cliente.updated_at) }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'ClienteCard',
  props: {
    cliente: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    showNotes: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    showQuickActions: {
      type: Boolean,
      default: true
    }
  },
  emits: ['view', 'edit', 'delete', 'new-rental', 'call', 'email'],
  setup(props, { emit }) {
    const quickActionsExpanded = ref(false)

    const getInitials = (nombre, apellidos) => {
      if (!nombre) return 'U'
      const n = nombre.charAt(0).toUpperCase()
      const a = apellidos ? apellidos.charAt(0).toUpperCase() : ''
      return n + a
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'posible': 'bg-warning',
        'confirmado': 'bg-info',
        'activo': 'bg-success',
        'inactivo': 'bg-secondary'
      }
      return classes[estado] || 'bg-secondary'
    }

    const getEstadoText = (estado) => {
      const texts = {
        'posible': 'Posible',
        'confirmado': 'Confirmado',
        'activo': 'Activo',
        'inactivo': 'Inactivo'
      }
      return texts[estado] || 'Desconocido'
    }

    const formatFecha = (fecha) => {
      if (!fecha) return ''
      return new Date(fecha).toLocaleDateString('es-ES')
    }

    const formatFechaRelativa = (fecha) => {
      if (!fecha) return ''
      
      const now = new Date()
      const date = new Date(fecha)
      const diff = now - date
      
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 1) return 'hace un momento'
      if (minutes < 60) return `hace ${minutes} min`
      if (hours < 24) return `hace ${hours} h`
      if (days < 7) return `hace ${days} días`
      
      return formatFecha(fecha)
    }

    const truncateText = (text, maxLength) => {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }

    const toggleQuickActions = () => {
      quickActionsExpanded.value = !quickActionsExpanded.value
    }

    const callClient = () => {
      if (props.cliente.telefono) {
        window.open(`tel:${props.cliente.telefono}`, '_blank')
        emit('call', props.cliente)
      }
    }

    const emailClient = () => {
      if (props.cliente.email) {
        window.open(`mailto:${props.cliente.email}`, '_blank')
        emit('email', props.cliente)
      }
    }

    return {
      quickActionsExpanded,
      getInitials,
      getEstadoClass,
      getEstadoText,
      formatFecha,
      formatFechaRelativa,
      truncateText,
      toggleQuickActions,
      callClient,
      emailClient
    }
  }
}
</script>

<style scoped>
.cliente-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.cliente-card:hover {
  transform: translateY(-2px);
}

.card {
  border: 1px solid #e3e6f0;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.card.border-primary {
  border-color: #4e73df !important;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

.cliente-name {
  color: #5a5c69;
  font-weight: 600;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
}

.contact-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.stats-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-value {
  font-weight: 600;
  color: #5a5c69;
}

.stat-label {
  color: #858796;
}

.notes {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 0.25rem;
  padding: 0.5rem;
}

.notes-content {
  display: flex;
  align-items: flex-start;
  font-size: 0.75rem;
}

.notes-text {
  color: #856404;
  line-height: 1.3;
}

.btn-group .btn {
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.quick-actions .btn {
  font-size: 0.7rem;
}

.card-footer {
  background-color: #f8f9fc;
  border-top: 1px solid #e3e6f0;
  padding: 0.5rem 1rem;
}

.badge {
  font-size: 0.65rem;
  font-weight: 500;
}

@media (max-width: 576px) {
  .stats-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .contact-item {
    font-size: 0.75rem;
  }
  
  .avatar-placeholder {
    width: 35px;
    height: 35px;
    font-size: 0.75rem;
  }
}
</style>

# Configuración Laravel Sanctum - Frontend y Backend

## ✅ Cambios Realizados en el Frontend

### 1. Configuración de Axios
- ✅ Cambiado `withCredentials: true` para permitir cookies
- ✅ URL base configurada desde variables de entorno
- ✅ Eliminada toda la lógica de CSRF tokens

### 2. Inicialización de Sanctum
- ✅ Agregada función `initializeSanctum()` que obtiene la cookie de sesión
- ✅ Limpieza automática de tokens CSRF obsoletos

## 🔧 Configuración Requerida en el Backend (Laravel)

### 1. Variables de Entorno (.env)
```env
# Dominio de la aplicación frontend
SANCTUM_STATEFUL_DOMAINS=localhost:3000,localhost:5173,localhost:8080
SESSION_DOMAIN=localhost
```

### 2. Configuración de CORS (config/cors.php)
```php
'paths' => ['api/*', 'sanctum/csrf-cookie'],
'allowed_methods' => ['*'],
'allowed_origins' => ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:8080'],
'allowed_origins_patterns' => [],
'allowed_headers' => ['*'],
'exposed_headers' => [],
'max_age' => 0,
'supports_credentials' => true,
```

### 3. Configuración de Sanctum (config/sanctum.php)
```php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,localhost:5173,localhost:8080,127.0.0.1,127.0.0.1:8000,::1',
    Sanctum::currentApplicationUrlWithPort()
))),
```

### 4. Configuración de Sesión (config/session.php)
```php
'domain' => env('SESSION_DOMAIN', 'localhost'),
'same_site' => 'lax',
```

## 🚀 Pasos para Probar

### 1. Reiniciar el servidor de desarrollo
```bash
npm run dev
```

### 2. Verificar en las herramientas de desarrollador
- Abrir Network tab
- Verificar que la petición a `/sanctum/csrf-cookie` se ejecute correctamente
- Verificar que las cookies se establezcan correctamente

### 3. Probar login
- Intentar hacer login
- Verificar que no aparezcan errores de dominio en la consola

## 🐛 Solución de Problemas

### Error: "Cookie rechazada por dominio no válido"
1. Verificar que tanto frontend como backend usen `localhost` (no `127.0.0.1`)
2. Verificar configuración de SANCTUM_STATEFUL_DOMAINS
3. Verificar configuración de CORS

### Error: "CSRF token mismatch"
1. Verificar que `withCredentials: true` esté configurado
2. Verificar que la función `initializeSanctum()` se ejecute antes del login
3. Verificar configuración de sesión en Laravel

## 📝 URLs Importantes

- Frontend: `http://localhost:3000` (o el puerto que uses)
- Backend API: `http://localhost:8000/api`
- Sanctum Cookie: `http://localhost:8000/sanctum/csrf-cookie`

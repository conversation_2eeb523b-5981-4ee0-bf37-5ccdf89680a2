<template>
  <div class="alquiler-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Nuevo' }} Alquiler</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del alquiler' : 'Registra un nuevo contrato de alquiler' }}</p>
        </div>
      </div>
      
      <form @submit.prevent="handleSubmit">
        <div class="row">
          <!-- Información del Cliente -->
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-user me-2"></i>Cliente
                </h5>
              </div>
              <div class="card-body">
                <ClienteSelector
                  v-model="form.cliente_id"
                  :error="errors.cliente_id"
                  filter-by-status="confirmado"
                  help-text="Selecciona el cliente que firmará el contrato"
                  @change="handleClienteChange"
                  @create-cliente="showCreateClienteModal = true"
                />
                
                <!-- Información del cliente seleccionado -->
                <div v-if="selectedCliente" class="mt-3">
                  <div class="alert alert-info py-2">
                    <small>
                      <strong>{{ selectedCliente.nombre }} {{ selectedCliente.apellidos }}</strong><br>
                      DNI: {{ selectedCliente.dni }} | Tel: {{ selectedCliente.telefono }}<br>
                      Email: {{ selectedCliente.email }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Información de la Propiedad -->
          <div class="col-lg-6">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-building me-2"></i>Propiedad
                </h5>
              </div>
              <div class="card-body">
                <PropiedadSelector
                  v-model="form.propiedad"
                  :error="errors.propiedad_id"
                  filter-by-status="disponible"
                  help-text="Selecciona la propiedad a alquilar"
                  @change="handlePropiedadChange"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- Detalles del Contrato -->
        <div class="row">
          <div class="col-12">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-file-contract me-2"></i>Detalles del Contrato
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <!-- Fechas del contrato -->
                  <div class="col-lg-6 mb-3">
                    <DateRangePicker
                      v-model="form.fechas"
                      start-label="Fecha de inicio"
                      end-label="Fecha de fin"
                      :min-start-date="today"
                      help-text="Define la duración del contrato de alquiler"
                      @validation-change="handleDateValidation"
                    />
                  </div>
                  
                  <!-- Precios -->
                  <div class="col-lg-6">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <PriceInput
                          v-model="form.valor"
                          label="Precio mensual"
                          :error="errors.valor"
                          show-per-month
                          :show-calculations="true"
                          help-text="Precio mensual del alquiler"
                          required
                        />
                      </div>
                      <div class="col-md-6 mb-3">
                        <PriceInput
                          v-model="form.fianza"
                          label="Fianza"
                          :error="errors.fianza"
                          help-text="Importe de la fianza (opcional)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Condiciones adicionales -->
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Día de cobro</label>
                    <select v-model="form.dia_cobro" class="form-select" :class="{ 'is-invalid': errors.dia_cobro }">
                      <option value="">Selecciona el día...</option>
                      <option v-for="dia in diasDelMes" :key="dia" :value="dia">
                        Día {{ dia }} de cada mes
                      </option>
                    </select>
                    <div v-if="errors.dia_cobro" class="invalid-feedback">
                      {{ errors.dia_cobro }}
                    </div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Estado inicial</label>
                    <select v-model="form.estado" class="form-select" :class="{ 'is-invalid': errors.estado }">
                      <option value="pendiente">Pendiente de firma</option>
                      <option value="activo">Activo</option>
                      <option value="borrador">Borrador</option>
                    </select>
                    <div v-if="errors.estado" class="invalid-feedback">
                      {{ errors.estado }}
                    </div>
                  </div>
                </div>
                
                <!-- Observaciones -->
                <div class="row">
                  <div class="col-12 mb-3">
                    <label class="form-label">Observaciones</label>
                    <textarea 
                      v-model="form.observaciones" 
                      class="form-control" 
                      rows="3"
                      placeholder="Condiciones especiales, observaciones del contrato..."
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Resumen del contrato -->
        <div v-if="showResumen" class="row">
          <div class="col-12">
            <div class="card mb-4 border-success">
              <div class="card-header bg-light-success">
                <h5 class="card-title mb-0 text-success">
                  <i class="fas fa-check-circle me-2"></i>Resumen del Contrato
                </h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <strong>Cliente:</strong><br>
                    <small>{{ selectedCliente?.nombre }} {{ selectedCliente?.apellidos }}</small>
                  </div>
                  <div class="col-md-3">
                    <strong>Propiedad:</strong><br>
                    <small>{{ getPropiedadResumen() }}</small>
                  </div>
                  <div class="col-md-3">
                    <strong>Duración:</strong><br>
                    <small>{{ getDuracionResumen() }}</small>
                  </div>
                  <div class="col-md-3">
                    <strong>Importe total:</strong><br>
                    <small class="text-success fs-6">{{ getImporteTotalResumen() }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <router-link to="/alquileres" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>Cancelar
              </router-link>
              <button 
                type="button" 
                class="btn btn-outline-primary"
                @click="saveDraft"
                :disabled="isSubmitting"
              >
                <i class="fas fa-save me-1"></i>Guardar borrador
              </button>
              <button 
                type="submit" 
                class="btn btn-primary"
                :disabled="isSubmitting || !isFormValid"
              >
                <i class="fas fa-check me-1"></i>
                {{ isSubmitting ? 'Guardando...' : (isEdit ? 'Actualizar' : 'Crear contrato') }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'vue-toastification'
import { alquileresService } from '@/services/alquileres'
import { clientesService } from '@/services/clientes'
import ClienteSelector from '@/components/common/ClienteSelector.vue'
import PropiedadSelector from '@/components/common/PropiedadSelector.vue'
import DateRangePicker from '@/components/common/DateRangePicker.vue'
import PriceInput from '@/components/common/PriceInput.vue'
import moment from 'moment'

export default {
  name: 'AlquilerForm',
  components: {
    ClienteSelector,
    PropiedadSelector,
    DateRangePicker,
    PriceInput
  },
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const selectedCliente = ref(null)
    const selectedPropiedad = ref(null)
    const errors = ref({})
    const dateValidation = ref({ isValid: true, errors: {} })
    
    const form = ref({
      cliente_id: null,
      propiedad: { tipo: '', id: null },
      fechas: { start: null, end: null },
      valor: null,
      fianza: null,
      dia_cobro: 1,
      estado: 'borrador',
      observaciones: ''
    })
    
    const today = computed(() => moment().format('YYYY-MM-DD'))
    const isEdit = computed(() => !!props.id)
    
    const diasDelMes = computed(() => {
      return Array.from({ length: 28 }, (_, i) => i + 1)
    })
    
    const isFormValid = computed(() => {
      return form.value.cliente_id && 
             form.value.propiedad.id && 
             form.value.fechas.start && 
             form.value.fechas.end && 
             form.value.valor && 
             dateValidation.value.isValid &&
             Object.keys(errors.value).length === 0
    })
    
    const showResumen = computed(() => {
      return selectedCliente.value && 
             selectedPropiedad.value && 
             form.value.fechas.start && 
             form.value.fechas.end && 
             form.value.valor
    })
    
    const handleClienteChange = async (cliente) => {
      selectedCliente.value = cliente
      if (cliente) {
        errors.value.cliente_id = null
      }
    }

    const handlePropiedadChange = (propiedadData) => {
      selectedPropiedad.value = propiedadData.data
      form.value.propiedad = { tipo: propiedadData.tipo, id: propiedadData.id }

      // Auto-completar precio si la propiedad tiene precio definido
      if (propiedadData.data?.precio && !form.value.valor) {
        form.value.valor = propiedadData.data.precio
      }

      if (propiedadData.id) {
        errors.value.propiedad_id = null
      }
    }

    const handleDateValidation = (validation) => {
      dateValidation.value = validation
    }

    const getPropiedadResumen = () => {
      if (!selectedPropiedad.value) return ''

      if (form.value.propiedad.tipo === 'trastero') {
        return `Trastero ${selectedPropiedad.value.numero} - Planta ${selectedPropiedad.value.planta}`
      } else {
        return `${selectedPropiedad.value.direccion} - Piso ${selectedPropiedad.value.piso}`
      }
    }

    const getDuracionResumen = () => {
      if (!form.value.fechas.start || !form.value.fechas.end) return ''

      const start = moment(form.value.fechas.start)
      const end = moment(form.value.fechas.end)
      const months = end.diff(start, 'months')

      return `${months} ${months === 1 ? 'mes' : 'meses'} (${start.format('DD/MM/YYYY')} - ${end.format('DD/MM/YYYY')})`
    }

    const getImporteTotalResumen = () => {
      if (!form.value.fechas.start || !form.value.fechas.end || !form.value.valor) return ''

      const start = moment(form.value.fechas.start)
      const end = moment(form.value.fechas.end)
      const months = end.diff(start, 'months')
      const total = months * form.value.valor

      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(total)
    }

    const validateForm = () => {
      errors.value = {}

      if (!form.value.cliente_id) {
        errors.value.cliente_id = 'Debe seleccionar un cliente'
      }

      if (!form.value.propiedad.id) {
        errors.value.propiedad_id = 'Debe seleccionar una propiedad'
      }

      if (!form.value.valor) {
        errors.value.valor = 'El precio mensual es obligatorio'
      }

      if (!form.value.dia_cobro) {
        errors.value.dia_cobro = 'Debe seleccionar el día de cobro'
      }

      return Object.keys(errors.value).length === 0 && dateValidation.value.isValid
    }

    const saveDraft = async () => {
      form.value.estado = 'borrador'
      await handleSubmit()
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isSubmitting.value = true

      try {
        const submitData = {
          cliente_id: form.value.cliente_id,
          tipo_propiedad: form.value.propiedad.tipo === 'trastero' ? 'App\\Trastero' : 'App\\Piso',
          propiedad_id: form.value.propiedad.id,
          fecha_inicio: form.value.fechas.start,
          fecha_fin: form.value.fechas.end,
          valor: form.value.valor,
          fianza: form.value.fianza || 0,
          dia_cobro: form.value.dia_cobro,
          estado: form.value.estado,
          observaciones: form.value.observaciones
        }

        if (isEdit.value) {
          await alquileresService.update(props.id, submitData)
          toast.success('Alquiler actualizado correctamente')
        } else {
          await alquileresService.create(submitData)
          toast.success('Alquiler creado correctamente')
        }

        router.push('/alquileres')
      } catch (error) {
        console.error('Error al guardar alquiler:', error)
        const message = error.response?.data?.message || 'Error al guardar el alquiler'
        toast.error(message)

        // Manejar errores de validación del servidor
        if (error.response?.data?.errors) {
          errors.value = error.response.data.errors
        }
      } finally {
        isSubmitting.value = false
      }
    }

    // Cargar datos si es edición
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await alquileresService.getById(props.id)
          const alquiler = response.data

          form.value = {
            cliente_id: alquiler.cliente_id,
            propiedad: {
              tipo: alquiler.tipo_propiedad === 'App\\Trastero' ? 'trastero' : 'piso',
              id: alquiler.propiedad_id
            },
            fechas: {
              start: alquiler.fecha_inicio,
              end: alquiler.fecha_fin
            },
            valor: alquiler.valor,
            fianza: alquiler.fianza,
            dia_cobro: alquiler.dia_cobro,
            estado: alquiler.estado,
            observaciones: alquiler.observaciones || ''
          }

          // Cargar datos del cliente
          if (alquiler.cliente) {
            selectedCliente.value = alquiler.cliente
          }

        } catch (error) {
          console.error('Error al cargar alquiler:', error)
          toast.error('Error al cargar los datos del alquiler')
          router.push('/alquileres')
        }
      }
    })

    return {
      form,
      errors,
      isEdit,
      isSubmitting,
      selectedCliente,
      selectedPropiedad,
      today,
      diasDelMes,
      isFormValid,
      showResumen,
      dateValidation,
      handleClienteChange,
      handlePropiedadChange,
      handleDateValidation,
      getPropiedadResumen,
      getDuracionResumen,
      getImporteTotalResumen,
      saveDraft,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.alquiler-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.alquiler-form .card-title {
  color: #495057;
  font-weight: 600;
}

.bg-light-success {
  background-color: #d1e7dd !important;
}

.alquiler-form .alert {
  border: none;
  border-radius: 0.375rem;
}

.alquiler-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.alquiler-form .gap-2 {
  gap: 0.5rem !important;
}

.alquiler-form .form-control:focus,
.alquiler-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.alquiler-form .text-success {
  color: #198754 !important;
}

.alquiler-form .border-success {
  border-color: #198754 !important;
}

.alquiler-form .fs-6 {
  font-size: 1rem !important;
  font-weight: 600;
}
</style>

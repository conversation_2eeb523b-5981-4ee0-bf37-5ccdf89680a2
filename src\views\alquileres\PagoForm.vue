<template>
  <div class="pago-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">Registrar Pago</h1>
          <p class="text-muted">Registra un nuevo pago para el contrato de alquiler</p>
        </div>
      </div>
      
      <form @submit.prevent="handleSubmit">
        <div class="row">
          <!-- Información del contrato -->
          <div class="col-lg-8">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-file-contract me-2"></i>Información del Contrato
                </h5>
              </div>
              <div class="card-body">
                <div v-if="alquiler" class="row">
                  <div class="col-md-6">
                    <div class="info-item">
                      <label class="fw-bold">Cliente:</label>
                      <div>{{ alquiler.cliente?.nombre }} {{ alquiler.cliente?.apellidos }}</div>
                      <small class="text-muted">{{ alquiler.cliente?.dni }}</small>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="info-item">
                      <label class="fw-bold">Propiedad:</label>
                      <div>{{ formatPropiedad(alquiler) }}</div>
                      <small class="text-muted">{{ formatPrecio(alquiler.valor) }}/mes</small>
                    </div>
                  </div>
                  <div class="col-md-6 mt-3">
                    <div class="info-item">
                      <label class="fw-bold">Período del contrato:</label>
                      <div>{{ formatFecha(alquiler.fecha_inicio) }} - {{ formatFecha(alquiler.fecha_fin) }}</div>
                    </div>
                  </div>
                  <div class="col-md-6 mt-3">
                    <div class="info-item">
                      <label class="fw-bold">Estado del contrato:</label>
                      <span :class="getEstadoClass(alquiler.estado)">
                        {{ alquiler.estado }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div v-else class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Cargando...</span>
                  </div>
                  <p class="mt-2 text-muted">Cargando información del contrato...</p>
                </div>
              </div>
            </div>
            
            <!-- Detalles del pago -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-euro-sign me-2"></i>Detalles del Pago
                </h5>
              </div>
              <div class="card-body">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <PriceInput
                      v-model="form.importe"
                      label="Importe del pago *"
                      :error="errors.importe"
                      help-text="Cantidad pagada por el cliente"
                      required
                    />
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Fecha del pago *</label>
                    <input 
                      v-model="form.fecha_pago" 
                      type="date" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.fecha_pago }"
                      :max="today"
                      required
                    >
                    <div v-if="errors.fecha_pago" class="invalid-feedback">
                      {{ errors.fecha_pago }}
                    </div>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Método de pago *</label>
                    <select 
                      v-model="form.metodo_pago" 
                      class="form-select" 
                      :class="{ 'is-invalid': errors.metodo_pago }"
                      required
                    >
                      <option value="">Selecciona...</option>
                      <option value="efectivo">Efectivo</option>
                      <option value="transferencia">Transferencia bancaria</option>
                      <option value="tarjeta">Tarjeta de crédito/débito</option>
                      <option value="cheque">Cheque</option>
                      <option value="bizum">Bizum</option>
                      <option value="paypal">PayPal</option>
                      <option value="otro">Otro</option>
                    </select>
                    <div v-if="errors.metodo_pago" class="invalid-feedback">
                      {{ errors.metodo_pago }}
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Referencia/Número</label>
                    <input 
                      v-model="form.referencia" 
                      type="text" 
                      class="form-control" 
                      :class="{ 'is-invalid': errors.referencia }"
                      placeholder="Número de transferencia, cheque, etc."
                    >
                    <div v-if="errors.referencia" class="invalid-feedback">
                      {{ errors.referencia }}
                    </div>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Período que cubre</label>
                    <DateRangePicker
                      v-model="form.periodo"
                      start-label="Desde"
                      end-label="Hasta"
                      :show-quick-buttons="false"
                      :show-range-info="true"
                      :show-monthly-info="true"
                      help-text="Período de alquiler que cubre este pago"
                      @validation-change="handlePeriodoValidation"
                    />
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Concepto</label>
                    <select 
                      v-model="form.concepto" 
                      class="form-select" 
                      :class="{ 'is-invalid': errors.concepto }"
                    >
                      <option value="alquiler">Alquiler mensual</option>
                      <option value="fianza">Fianza</option>
                      <option value="gastos">Gastos de comunidad</option>
                      <option value="recargo">Recargo por mora</option>
                      <option value="reparacion">Reparación</option>
                      <option value="otro">Otro concepto</option>
                    </select>
                    <div v-if="errors.concepto" class="invalid-feedback">
                      {{ errors.concepto }}
                    </div>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Observaciones</label>
                    <textarea 
                      v-model="form.observaciones" 
                      class="form-control" 
                      rows="3"
                      placeholder="Observaciones adicionales sobre el pago..."
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Resumen y acciones -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-calculator me-2"></i>Resumen
                </h5>
              </div>
              <div class="card-body">
                <div v-if="alquiler" class="resumen-pago">
                  <div class="row mb-2">
                    <div class="col-6"><strong>Alquiler mensual:</strong></div>
                    <div class="col-6 text-end">{{ formatPrecio(alquiler.valor) }}</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-6"><strong>Importe a pagar:</strong></div>
                    <div class="col-6 text-end text-primary fs-5">
                      {{ form.importe ? formatPrecio(form.importe) : '€0,00' }}
                    </div>
                  </div>
                  <hr>
                  <div v-if="form.importe && alquiler.valor" class="row">
                    <div class="col-12">
                      <div v-if="form.importe == alquiler.valor" class="alert alert-success py-2">
                        <i class="fas fa-check-circle me-1"></i>
                        <small>Pago completo del mes</small>
                      </div>
                      <div v-else-if="form.importe < alquiler.valor" class="alert alert-warning py-2">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <small>Pago parcial (falta {{ formatPrecio(alquiler.valor - form.importe) }})</small>
                      </div>
                      <div v-else class="alert alert-info py-2">
                        <i class="fas fa-info-circle me-1"></i>
                        <small>Pago superior al alquiler mensual</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Estado del pago -->
            <div class="card mb-4">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fas fa-check-circle me-2"></i>Estado
                </h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">Estado del pago</label>
                  <select 
                    v-model="form.estado" 
                    class="form-select" 
                    :class="{ 'is-invalid': errors.estado }"
                  >
                    <option value="confirmado">Confirmado</option>
                    <option value="pendiente">Pendiente de confirmación</option>
                    <option value="rechazado">Rechazado</option>
                  </select>
                  <div v-if="errors.estado" class="invalid-feedback">
                    {{ errors.estado }}
                  </div>
                </div>
                
                <div class="form-check">
                  <input 
                    v-model="form.generar_recibo" 
                    class="form-check-input" 
                    type="checkbox" 
                    id="generar_recibo"
                  >
                  <label class="form-check-label" for="generar_recibo">
                    Generar recibo automáticamente
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Botones de acción -->
        <div class="row">
          <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
              <router-link :to="`/alquileres/${alquilerId}`" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>Cancelar
              </router-link>
              <button 
                type="submit" 
                class="btn btn-primary"
                :disabled="isSubmitting || !isFormValid"
              >
                <i class="fas fa-save me-1"></i>
                {{ isSubmitting ? 'Registrando...' : 'Registrar pago' }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'vue-toastification'
import { alquileresService } from '@/services/alquileres'
import PriceInput from '@/components/common/PriceInput.vue'
import DateRangePicker from '@/components/common/DateRangePicker.vue'
import moment from 'moment'

export default {
  name: 'PagoForm',
  components: {
    PriceInput,
    DateRangePicker
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const toast = useToast()
    
    const alquilerId = route.params.id
    const isSubmitting = ref(false)
    const alquiler = ref(null)
    const errors = ref({})
    const periodoValidation = ref({ isValid: true, errors: {} })
    
    const form = ref({
      importe: null,
      fecha_pago: moment().format('YYYY-MM-DD'),
      metodo_pago: '',
      referencia: '',
      periodo: { start: null, end: null },
      concepto: 'alquiler',
      observaciones: '',
      estado: 'confirmado',
      generar_recibo: true
    })
    
    const today = computed(() => moment().format('YYYY-MM-DD'))
    
    const isFormValid = computed(() => {
      return form.value.importe && 
             form.value.fecha_pago && 
             form.value.metodo_pago &&
             periodoValidation.value.isValid &&
             Object.keys(errors.value).length === 0
    })
    
    const formatPropiedad = (alquiler) => {
      if (alquiler.tipo_propiedad === 'App\\Trastero') {
        return `Trastero ${alquiler.propiedad?.numero || alquiler.propiedad_id}`
      } else {
        return `Piso ${alquiler.propiedad?.direccion || alquiler.propiedad_id}`
      }
    }

    const formatPrecio = (precio) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(precio)
    }

    const formatFecha = (fecha) => {
      return moment(fecha).format('DD/MM/YYYY')
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'activo': 'badge bg-success',
        'pendiente': 'badge bg-warning',
        'vencido': 'badge bg-danger',
        'finalizado': 'badge bg-secondary'
      }
      return classes[estado] || 'badge bg-secondary'
    }

    const handlePeriodoValidation = (validation) => {
      periodoValidation.value = validation
    }

    const validateForm = () => {
      errors.value = {}

      if (!form.value.importe || form.value.importe <= 0) {
        errors.value.importe = 'El importe debe ser mayor a 0'
      }

      if (!form.value.fecha_pago) {
        errors.value.fecha_pago = 'La fecha de pago es obligatoria'
      }

      if (!form.value.metodo_pago) {
        errors.value.metodo_pago = 'Debe seleccionar el método de pago'
      }

      if (form.value.fecha_pago && moment(form.value.fecha_pago).isAfter(moment(), 'day')) {
        errors.value.fecha_pago = 'La fecha de pago no puede ser futura'
      }

      return Object.keys(errors.value).length === 0 && periodoValidation.value.isValid
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isSubmitting.value = true

      try {
        const submitData = {
          importe: parseFloat(form.value.importe),
          fecha_pago: form.value.fecha_pago,
          metodo_pago: form.value.metodo_pago,
          referencia: form.value.referencia,
          periodo_inicio: form.value.periodo.start,
          periodo_fin: form.value.periodo.end,
          concepto: form.value.concepto,
          observaciones: form.value.observaciones,
          estado: form.value.estado,
          generar_recibo: form.value.generar_recibo
        }

        await alquileresService.registrarPago(alquilerId, submitData)
        toast.success('Pago registrado correctamente')
        router.push(`/alquileres/${alquilerId}`)
      } catch (error) {
        console.error('Error al registrar pago:', error)
        const message = error.response?.data?.message || 'Error al registrar el pago'
        toast.error(message)

        if (error.response?.data?.errors) {
          errors.value = error.response.data.errors
        }
      } finally {
        isSubmitting.value = false
      }
    }

    // Cargar datos del alquiler
    onMounted(async () => {
      try {
        const response = await alquileresService.getById(alquilerId)
        alquiler.value = response.data

        // Pre-llenar algunos campos
        form.value.importe = alquiler.value.valor

        // Calcular período sugerido (mes actual)
        const hoy = moment()
        form.value.periodo = {
          start: hoy.startOf('month').format('YYYY-MM-DD'),
          end: hoy.endOf('month').format('YYYY-MM-DD')
        }

      } catch (error) {
        console.error('Error al cargar alquiler:', error)
        toast.error('Error al cargar los datos del contrato')
        router.push('/alquileres')
      }
    })

    return {
      alquilerId,
      alquiler,
      form,
      errors,
      today,
      isSubmitting,
      isFormValid,
      periodoValidation,
      formatPropiedad,
      formatPrecio,
      formatFecha,
      getEstadoClass,
      handlePeriodoValidation,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.pago-form .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.pago-form .card-title {
  color: #495057;
  font-weight: 600;
}

.info-item {
  margin-bottom: 1rem;
}

.info-item label {
  display: block;
  margin-bottom: 0.25rem;
  color: #6c757d;
  font-size: 0.875rem;
}

.resumen-pago {
  font-size: 0.9rem;
}

.resumen-pago .row {
  margin-bottom: 0.5rem;
}

.pago-form .form-check {
  padding-left: 1.5rem;
}

.pago-form .form-check-input {
  margin-left: -1.5rem;
}

.pago-form .form-check-label {
  font-weight: 500;
  color: #495057;
}

.pago-form .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.pago-form .gap-2 {
  gap: 0.5rem !important;
}

.pago-form .form-control:focus,
.pago-form .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.pago-form .is-invalid {
  border-color: #dc3545;
}

.pago-form .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.pago-form .alert {
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.pago-form .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.pago-form .spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pago-form .card-body {
    padding: 1rem;
  }

  .pago-form .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .pago-form .d-flex {
    flex-direction: column;
  }
}
</style>

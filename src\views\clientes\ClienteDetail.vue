<template>
  <div class="cliente-detail">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">
                <i class="fas fa-user me-2"></i>
                {{ cliente?.nombre }} {{ cliente?.apellidos }}
              </h1>
              <p class="text-muted mb-0">
                Información detallada del cliente
              </p>
            </div>
            <div class="d-flex gap-2">
              <router-link to="/clientes" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Volver al listado
              </router-link>
              <router-link
                :to="`/clientes/${id}/editar`"
                class="btn btn-warning"
              >
                <i class="fas fa-edit me-1"></i>Editar Cliente
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <p class="mt-2 text-muted">Cargando información del cliente...</p>
      </div>

      <!-- Error -->
      <div v-else-if="error" class="row">
        <div class="col-12">
          <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
          </div>
        </div>
      </div>

      <!-- Contenido principal -->
      <div v-else-if="cliente" class="row">
        <!-- Información del cliente -->
        <div class="col-lg-4 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-id-card me-2"></i>
                Información Personal
              </h5>
            </div>
            <div class="card-body">
              <div class="text-center mb-4">
                <div class="avatar-large mx-auto mb-3">
                  <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center">
                    {{ getInitials(cliente.nombre, cliente.apellidos) }}
                  </div>
                </div>
                <h5 class="mb-1">{{ cliente.nombre }} {{ cliente.apellidos }}</h5>
                <span :class="getEstadoClass(cliente.estado)" class="badge">
                  {{ getEstadoText(cliente.estado) }}
                </span>
              </div>

              <div class="info-group">
                <div class="info-item">
                  <i class="fas fa-id-card text-muted me-2"></i>
                  <strong>DNI/NIE:</strong> {{ cliente.dni }}
                </div>
                <div class="info-item">
                  <i class="fas fa-envelope text-muted me-2"></i>
                  <strong>Email:</strong>
                  <a :href="`mailto:${cliente.email}`">{{ cliente.email }}</a>
                </div>
                <div class="info-item">
                  <i class="fas fa-phone text-muted me-2"></i>
                  <strong>Teléfono:</strong>
                  <a :href="`tel:${cliente.telefono}`">{{ cliente.telefono }}</a>
                </div>
                <div v-if="cliente.direccion" class="info-item">
                  <i class="fas fa-map-marker-alt text-muted me-2"></i>
                  <strong>Dirección:</strong> {{ cliente.direccion }}
                </div>
                <div class="info-item">
                  <i class="fas fa-calendar text-muted me-2"></i>
                  <strong>Fecha de registro:</strong> {{ formatFecha(cliente.created_at) }}
                </div>
                <div v-if="cliente.updated_at !== cliente.created_at" class="info-item">
                  <i class="fas fa-clock text-muted me-2"></i>
                  <strong>Última actualización:</strong> {{ formatFecha(cliente.updated_at) }}
                </div>
              </div>

              <div v-if="cliente.notas" class="mt-4">
                <h6 class="text-primary">
                  <i class="fas fa-sticky-note me-2"></i>
                  Notas
                </h6>
                <div class="notes-content">
                  {{ cliente.notas }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Estadísticas y acciones -->
        <div class="col-lg-8">
          <!-- Estadísticas rápidas -->
          <div class="row mb-4">
            <div class="col-md-4">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h4 class="mb-0">{{ alquileres.length }}</h4>
                      <p class="mb-0">Alquileres</p>
                    </div>
                    <div class="align-self-center">
                      <i class="fas fa-file-contract fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h4 class="mb-0">{{ alquileresActivos }}</h4>
                      <p class="mb-0">Activos</p>
                    </div>
                    <div class="align-self-center">
                      <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h4 class="mb-0">{{ formatPrecio(totalFacturado) }}</h4>
                      <p class="mb-0">Total Facturado</p>
                    </div>
                    <div class="align-self-center">
                      <i class="fas fa-euro-sign fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Historial de alquileres -->
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="fas fa-file-contract me-2"></i>
                Historial de Alquileres
              </h5>
              <router-link to="/alquileres/nuevo" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>Nuevo Alquiler
              </router-link>
            </div>
            <div class="card-body">
              <!-- Loading alquileres -->
              <div v-if="isLoadingAlquileres" class="text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <span class="ms-2">Cargando alquileres...</span>
              </div>

              <!-- Sin alquileres -->
              <div v-else-if="alquileres.length === 0" class="text-center py-4">
                <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                <h6>Sin alquileres</h6>
                <p class="text-muted mb-3">Este cliente aún no tiene alquileres registrados</p>
                <router-link to="/alquileres/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Crear Primer Alquiler
                </router-link>
              </div>

              <!-- Lista de alquileres -->
              <div v-else class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Propiedad</th>
                      <th>Precio</th>
                      <th>Estado</th>
                      <th>Fecha Inicio</th>
                      <th>Fecha Fin</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="alquiler in alquileres" :key="alquiler.id">
                      <td>
                        <div>
                          <div class="fw-bold">{{ formatPropiedad(alquiler) }}</div>
                          <div class="text-muted small">{{ alquiler.tipo_propiedad }}</div>
                        </div>
                      </td>
                      <td>
                        <span class="fw-bold">{{ formatPrecio(alquiler.precio_mensual) }}</span>
                        <div class="text-muted small">mensual</div>
                      </td>
                      <td>
                        <span :class="getAlquilerEstadoClass(alquiler.estado)" class="badge">
                          {{ getAlquilerEstadoText(alquiler.estado) }}
                        </span>
                      </td>
                      <td>{{ formatFecha(alquiler.fecha_inicio) }}</td>
                      <td>{{ alquiler.fecha_fin ? formatFecha(alquiler.fecha_fin) : 'Indefinido' }}</td>
                      <td>
                        <div class="btn-group" role="group">
                          <router-link
                            :to="`/alquileres/${alquiler.id}`"
                            class="btn btn-sm btn-outline-primary"
                            title="Ver detalle"
                          >
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link
                            :to="`/alquileres/${alquiler.id}/editar`"
                            class="btn btn-sm btn-outline-warning"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Acciones rápidas -->
      <div v-if="cliente" class="row mt-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-bolt me-2"></i>
                Acciones Rápidas
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3 mb-2">
                  <router-link
                    :to="`/alquileres/nuevo?cliente=${cliente.id}`"
                    class="btn btn-primary w-100"
                  >
                    <i class="fas fa-plus me-2"></i>
                    Nuevo Alquiler
                  </router-link>
                </div>
                <div class="col-md-3 mb-2">
                  <button
                    @click="enviarEmail"
                    class="btn btn-info w-100"
                    :disabled="!cliente.email"
                  >
                    <i class="fas fa-envelope me-2"></i>
                    Enviar Email
                  </button>
                </div>
                <div class="col-md-3 mb-2">
                  <button
                    @click="llamarCliente"
                    class="btn btn-success w-100"
                    :disabled="!cliente.telefono"
                  >
                    <i class="fas fa-phone me-2"></i>
                    Llamar Cliente
                  </button>
                </div>
                <div class="col-md-3 mb-2">
                  <button
                    @click="generarReporte"
                    class="btn btn-secondary w-100"
                  >
                    <i class="fas fa-file-pdf me-2"></i>
                    Generar Reporte
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { clientesService } from '@/services/clientes'
import { alquileresService } from '@/services/alquileres'

export default {
  name: 'ClienteDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()

    // Estado reactivo
    const cliente = ref(null)
    const alquileres = ref([])
    const isLoading = ref(false)
    const isLoadingAlquileres = ref(false)
    const error = ref(null)

    // Computed properties
    const alquileresActivos = computed(() => {
      return alquileres.value.filter(a => a.estado === 'activo').length
    })

    const totalFacturado = computed(() => {
      return alquileres.value.reduce((total, alquiler) => {
        return total + (parseFloat(alquiler.precio_mensual) || 0)
      }, 0)
    })

    // Métodos
    const loadCliente = async () => {
      isLoading.value = true
      error.value = null

      try {
        const response = await clientesService.getById(props.id)
        cliente.value = response.data
      } catch (err) {
        console.error('Error al cargar cliente:', err)
        error.value = 'Error al cargar la información del cliente'

        if (err.response?.status === 404) {
          error.value = 'Cliente no encontrado'
          setTimeout(() => {
            router.push('/clientes')
          }, 3000)
        }
      } finally {
        isLoading.value = false
      }
    }

    const loadAlquileres = async () => {
      isLoadingAlquileres.value = true

      try {
        const response = await alquileresService.getPorCliente(props.id)
        alquileres.value = Array.isArray(response.data) ? response.data : []
      } catch (err) {
        console.error('Error al cargar alquileres:', err)
        alquileres.value = []
      } finally {
        isLoadingAlquileres.value = false
      }
    }

    const getInitials = (nombre, apellidos) => {
      if (!nombre) return 'U'
      const n = nombre.charAt(0).toUpperCase()
      const a = apellidos ? apellidos.charAt(0).toUpperCase() : ''
      return n + a
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'posible': 'bg-warning',
        'confirmado': 'bg-info',
        'activo': 'bg-success',
        'inactivo': 'bg-secondary'
      }
      return classes[estado] || 'bg-secondary'
    }

    const getEstadoText = (estado) => {
      const texts = {
        'posible': 'Posible',
        'confirmado': 'Confirmado',
        'activo': 'Activo',
        'inactivo': 'Inactivo'
      }
      return texts[estado] || 'Desconocido'
    }

    const getAlquilerEstadoClass = (estado) => {
      const classes = {
        'activo': 'bg-success',
        'pendiente': 'bg-warning',
        'vencido': 'bg-danger',
        'finalizado': 'bg-secondary'
      }
      return classes[estado] || 'bg-secondary'
    }

    const getAlquilerEstadoText = (estado) => {
      const texts = {
        'activo': 'Activo',
        'pendiente': 'Pendiente',
        'vencido': 'Vencido',
        'finalizado': 'Finalizado'
      }
      return texts[estado] || 'Desconocido'
    }

    const formatFecha = (fecha) => {
      if (!fecha) return ''
      return new Date(fecha).toLocaleDateString('es-ES')
    }

    const formatPrecio = (precio) => {
      if (!precio) return '0,00 €'
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(precio)
    }

    const formatPropiedad = (alquiler) => {
      if (alquiler.tipo_propiedad === 'trastero') {
        return `Trastero ${alquiler.trastero?.numero || alquiler.propiedad_id}`
      } else if (alquiler.tipo_propiedad === 'piso') {
        return `Piso ${alquiler.piso?.numero || alquiler.propiedad_id}`
      }
      return `Propiedad ${alquiler.propiedad_id}`
    }

    // Acciones rápidas
    const enviarEmail = () => {
      if (cliente.value?.email) {
        window.open(`mailto:${cliente.value.email}`, '_blank')
      } else {
        toast.warning('El cliente no tiene email registrado')
      }
    }

    const llamarCliente = () => {
      if (cliente.value?.telefono) {
        window.open(`tel:${cliente.value.telefono}`, '_blank')
      } else {
        toast.warning('El cliente no tiene teléfono registrado')
      }
    }

    const generarReporte = () => {
      // TODO: Implementar generación de reporte
      toast.info('Función de generar reporte por implementar')
    }

    // Lifecycle
    onMounted(async () => {
      await loadCliente()
      if (cliente.value) {
        await loadAlquileres()
      }
    })

    return {
      cliente,
      alquileres,
      isLoading,
      isLoadingAlquileres,
      error,
      alquileresActivos,
      totalFacturado,
      getInitials,
      getEstadoClass,
      getEstadoText,
      getAlquilerEstadoClass,
      getAlquilerEstadoText,
      formatFecha,
      formatPrecio,
      formatPropiedad,
      enviarEmail,
      llamarCliente,
      generarReporte
    }
  }
}
</script>

<style scoped>
.avatar-large {
  width: 80px;
  height: 80px;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
}

.info-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  font-size: 0.9rem;
  line-height: 1.4;
}

.info-item i {
  width: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.info-item strong {
  margin-right: 0.5rem;
  min-width: 100px;
  color: #5a5c69;
}

.info-item a {
  color: #4e73df;
  text-decoration: none;
}

.info-item a:hover {
  text-decoration: underline;
}

.notes-content {
  background-color: #f8f9fc;
  border: 1px solid #e3e6f0;
  border-radius: 0.375rem;
  padding: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.card-header h5 {
  color: #5a5c69;
  font-weight: 600;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.text-primary {
  color: #4e73df !important;
}

.text-muted {
  color: #858796 !important;
}

.btn-primary {
  background-color: #4e73df;
  border-color: #4e73df;
}

.btn-primary:hover {
  background-color: #2e59d9;
  border-color: #2653d4;
}

.btn-warning {
  background-color: #f6c23e;
  border-color: #f6c23e;
  color: #1a1a1a;
}

.btn-warning:hover {
  background-color: #f4b619;
  border-color: #f4b619;
  color: #1a1a1a;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

@media (max-width: 768px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.gap-2 {
    gap: 0.5rem !important;
  }

  .btn-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  .btn-group .btn:last-child {
    margin-bottom: 0;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-item strong {
    min-width: auto;
    margin-bottom: 0.25rem;
  }

  .avatar-large {
    width: 60px;
    height: 60px;
  }

  .avatar-placeholder {
    font-size: 1.2rem;
  }
}
</style>

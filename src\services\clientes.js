import api from './api'

export const clientesService = {
  // Obtener todos los clientes
  getAll(params = {}) {
    return api.get('/clientes', 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      }
    )
  },

  // Obtener cliente por ID
  getById(id) {
    return api.get(`/clientes/ver/${id}`, 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Crear nuevo cliente
  create(data) {
    return api.post('/clientes/create',
      { data },
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Actualizar cliente
  update(id, data) {
    return api.post(`/clientes/update/${id}`,
      { data },
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Eliminar cliente
  delete(id) {
    return api.delete(`/clientes/${id}`, 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Obtener clientes activos
  getActivos() {
    return api.get('/clientes-activos', 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Obtener posibles clientes
  getPosibles() {
    return api.get('/clientes-posibles', 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Obtener clientes confirmados
  getConfirmados() {
    return api.get('/clientes-confirmados', 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  },

  // Confirmar cliente
  confirmar(id) {
    return api.patch(`/clientes/${id}/confirmar`, 
      { headers: {
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
        ,withCredentials: true
      })
  }
}

import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Importar vistas
import Login from '@/views/auth/Login.vue'
import Dashboard from '@/views/Dashboard.vue'
import TrasterosList from '@/views/trasteros/TrasterosList.vue'
import TrasteroDetail from '@/views/trasteros/TrasteroDetail.vue'
import PisosList from '@/views/pisos/PisosList.vue'
import PisoDetail from '@/views/pisos/PisoDetail.vue'
import ClientesList from '@/views/clientes/ClientesList.vue'
import ClienteDetail from '@/views/clientes/ClienteDetail.vue'
import ClienteForm from '@/views/clientes/ClienteForm.vue'
import DocumentosList from '@/views/documentos/DocumentosList.vue'
import GastosList from '@/views/gastos/GastosList.vue'
import AlquileresList from '@/views/alquileres/AlquileresList.vue'
import Reportes from '@/views/reportes/Reportes.vue'
import { isTokenExpired } from '@/services/authService'


const routes = [
  // Rutas públicas
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  
  // Rutas protegidas
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  
  // Trasteros
  {
    path: '/trasteros',
    name: 'TrasterosList',
    component: TrasterosList,
    meta: { requiresAuth: true }
  },
  {
    path: '/trasteros/:id',
    name: 'TrasteroDetail',
    component: TrasteroDetail,
    meta: { requiresAuth: true },
    props: true
  },
  {
    path: '/trasteros/nuevo',
    name: 'TrasteroCreate',
    component: () => import('@/views/trasteros/TrasteroForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/trasteros/:id/editar',
    name: 'TrasteroEdit',
    component: () => import('@/views/trasteros/TrasteroForm.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  
  // Pisos
  {
    path: '/pisos',
    name: 'PisosList',
    component: PisosList,
    meta: { requiresAuth: true }
  },
  {
    path: '/pisos/:id',
    name: 'PisoDetail',
    component: PisoDetail,
    meta: { requiresAuth: true },
    props: true
  },
  {
    path: '/pisos/nuevo',
    name: 'PisoCreate',
    component: () => import('@/views/pisos/PisoForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/pisos/:id/editar',
    name: 'PisoEdit',
    component: () => import('@/views/pisos/PisoForm.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  
  // Clientes
  {
    path: '/clientes',
    name: 'ClientesList',
    component: ClientesList,
    meta: { requiresAuth: true }
  },
  {
    path: '/clientes/:id',
    name: 'ClienteDetail',
    component: ClienteDetail,
    meta: { requiresAuth: true },
    props: true
  },
  {
    path: '/clientes/nuevo',
    name: 'ClienteCreate',
    component: ClienteForm,
    meta: { requiresAuth: true }
  },
  {
    path: '/clientes/:id/editar',
    name: 'ClienteEdit',
    component: ClienteForm,
    meta: { requiresAuth: true },
    props: true
  },
  
  // Documentos
  {
    path: '/documentos',
    name: 'DocumentosList',
    component: DocumentosList,
    meta: { requiresAuth: true }
  },
  {
    path: '/documentos/nuevo',
    name: 'DocumentoCreate',
    component: () => import('@/views/documentos/DocumentoForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/documentos/:id/editar',
    name: 'DocumentoEdit',
    component: () => import('@/views/documentos/DocumentoForm.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  
  // Gastos
  {
    path: '/gastos',
    name: 'GastosList',
    component: GastosList,
    meta: { requiresAuth: true }
  },
  {
    path: '/gastos/nuevo',
    name: 'GastoCreate',
    component: () => import('@/views/gastos/GastoForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/gastos/:id/editar',
    name: 'GastoEdit',
    component: () => import('@/views/gastos/GastoForm.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  
  // Alquileres
  {
    path: '/alquileres',
    name: 'AlquileresList',
    component: AlquileresList,
    meta: { requiresAuth: true }
  },
  {
    path: '/alquileres/nuevo',
    name: 'AlquilerCreate',
    component: () => import('@/views/alquileres/AlquilerForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/alquileres/:id/editar',
    name: 'AlquilerEdit',
    component: () => import('@/views/alquileres/AlquilerForm.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  {
    path: '/alquileres/:id',
    name: 'AlquilerDetail',
    component: () => import('@/views/alquileres/AlquilerDetail.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  {
    path: '/alquileres/:id/pago',
    name: 'AlquilerPago',
    component: () => import('@/views/alquileres/PagoForm.vue'),
    meta: { requiresAuth: true },
    props: true
  },
  
  // Reportes
  {
    path: '/reportes',
    name: 'Reportes',
    component: Reportes,
    meta: { requiresAuth: true }
  },

  // Administración
  {
    path: '/admin/usuarios',
    name: 'UsuariosList',
    component: () => import('@/views/admin/UsuariosList.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/usuarios/nuevo',
    name: 'UsuarioCreate',
    component: () => import('@/views/admin/UsuarioForm.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/usuarios/:id/editar',
    name: 'UsuarioEdit',
    component: () => import('@/views/admin/UsuarioForm.vue'),
    meta: { requiresAuth: true, requiresAdmin: true },
    props: true
  },
  {
    path: '/admin/configuracion',
    name: 'Configuracion',
    component: () => import('@/views/admin/ConfiguracionForm.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },

  // Ruta 404
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach(async (to, from, next) => {
    const token = localStorage.getItem('token');
    //const expired = await isTokenExpired();
    if (to.meta.name !== 'Login') {
      //if (to.meta.requiresAuth && (!token || expired === true)) {
      if (to.meta.requiresAuth && (!token )) {
        next('/login');
      } else {
        next();
      }
    }
});

export default router






import api from './api'

export const pisosService = {
  // Obtener todos los pisos
  getAll(params = {}) {
    return api.get('/pisos', { params })
  },

  // Obtener piso por ID
  getById(id) {
    return api.get(`/pisos/${id}`)
  },

  // Crear nuevo piso
  create(data) {
    return api.post('/pisos', data)
  },

  // Actualizar piso
  update(id, data) {
    return api.put(`/pisos/${id}`, data)
  },

  // Eliminar piso
  delete(id) {
    return api.delete(`/pisos/${id}`)
  },

  // Obtener pisos disponibles
  getDisponibles() {
    return api.get('/pisos-disponibles')
  },

  // Obtener pisos ocupados
  getOcupados() {
    return api.get('/pisos-ocupados')
  }
}

<template>
  <div class="propiedad-selector">
    <label v-if="label" class="form-label">{{ label }}</label>
    
    <!-- Selector de tipo de propiedad -->
    <div class="row mb-2">
      <div class="col-md-4">
        <select 
          v-model="tipoPropiedad" 
          class="form-select form-select-sm"
          :disabled="disabled || isLoading"
          @change="handleTipoChange"
        >
          <option value="">Tipo de propiedad</option>
          <option value="trastero">Trastero</option>
          <option value="piso">Piso</option>
        </select>
      </div>
    </div>
    
    <!-- Selector de propiedad específica -->
    <div class="input-group">
      <select 
        v-model="selectedPropiedad" 
        class="form-select" 
        :class="{ 'is-invalid': error }"
        :disabled="disabled || isLoading || !tipoPropiedad"
        @change="handleChange"
      >
        <option value="">{{ getPlaceholder() }}</option>
        <option 
          v-for="propiedad in filteredPropiedades" 
          :key="propiedad.id" 
          :value="propiedad.id"
        >
          {{ formatPropiedadOption(propiedad) }}
        </option>
      </select>
      
      <button 
        v-if="showCreateButton && tipoPropiedad"
        type="button" 
        class="btn btn-outline-primary" 
        @click="$emit('create-propiedad', tipoPropiedad)"
        :disabled="disabled || isLoading"
      >
        <i class="fas fa-plus"></i>
      </button>
      
      <button 
        v-if="showRefreshButton"
        type="button" 
        class="btn btn-outline-secondary" 
        @click="loadPropiedades"
        :disabled="disabled || isLoading || !tipoPropiedad"
      >
        <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoading }"></i>
      </button>
    </div>
    
    <div v-if="error" class="invalid-feedback d-block">
      {{ error }}
    </div>
    
    <!-- Información de la propiedad seleccionada -->
    <div v-if="selectedPropiedadData" class="mt-2">
      <div class="card card-body bg-light">
        <div class="row">
          <div class="col-md-6">
            <small class="text-muted">
              <strong>{{ tipoPropiedad === 'trastero' ? 'Trastero' : 'Piso' }}:</strong>
              {{ formatPropiedadInfo(selectedPropiedadData) }}
            </small>
          </div>
          <div class="col-md-3">
            <small class="text-muted">
              <strong>Precio:</strong> {{ formatPrice(selectedPropiedadData.precio) }}
            </small>
          </div>
          <div class="col-md-3">
            <small class="text-muted">
              <strong>Estado:</strong> 
              <span :class="getEstadoClass(selectedPropiedadData.estado)">
                {{ selectedPropiedadData.estado }}
              </span>
            </small>
          </div>
        </div>
      </div>
    </div>
    
    <small v-if="helpText" class="form-text text-muted">
      {{ helpText }}
    </small>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { trasterosService } from '@/services/trasteros'
import { pisosService } from '@/services/pisos'
import { useToast } from 'vue-toastification'

export default {
  name: 'PropiedadSelector',
  props: {
    modelValue: {
      type: Object,
      default: () => ({ tipo: '', id: null })
    },
    label: {
      type: String,
      default: 'Propiedad'
    },
    error: {
      type: String,
      default: null
    },
    helpText: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showCreateButton: {
      type: Boolean,
      default: true
    },
    showRefreshButton: {
      type: Boolean,
      default: true
    },
    filterByStatus: {
      type: String,
      default: 'disponible' // 'disponible', 'ocupado', null
    },
    allowedTypes: {
      type: Array,
      default: () => ['trastero', 'piso']
    }
  },
  emits: ['update:modelValue', 'change', 'create-propiedad'],
  setup(props, { emit }) {
    const toast = useToast()
    const trasteros = ref([])
    const pisos = ref([])
    const isLoading = ref(false)
    
    const tipoPropiedad = ref(props.modelValue?.tipo || '')
    const selectedPropiedad = ref(props.modelValue?.id || null)
    
    const filteredPropiedades = computed(() => {
      const propiedades = tipoPropiedad.value === 'trastero' ? trasteros.value : pisos.value
      
      if (!props.filterByStatus) {
        return propiedades
      }
      
      return propiedades.filter(prop => prop.estado === props.filterByStatus)
    })
    
    const selectedPropiedadData = computed(() => {
      if (!selectedPropiedad.value || !tipoPropiedad.value) return null
      
      const propiedades = tipoPropiedad.value === 'trastero' ? trasteros.value : pisos.value
      return propiedades.find(p => p.id == selectedPropiedad.value)
    })
    
    const getPlaceholder = () => {
      if (!tipoPropiedad.value) {
        return 'Primero selecciona el tipo de propiedad'
      }
      return `Selecciona un ${tipoPropiedad.value}...`
    }
    
    const formatPropiedadOption = (propiedad) => {
      if (tipoPropiedad.value === 'trastero') {
        return `${propiedad.numero} - Planta ${propiedad.planta} (${propiedad.tamano}) - ${formatPrice(propiedad.precio)}`
      } else {
        return `${propiedad.direccion} - Piso ${propiedad.piso} (${propiedad.habitaciones}hab) - ${formatPrice(propiedad.precio)}`
      }
    }
    
    const formatPropiedadInfo = (propiedad) => {
      if (tipoPropiedad.value === 'trastero') {
        return `Nº ${propiedad.numero}, Planta ${propiedad.planta}, ${propiedad.tamano}`
      } else {
        return `${propiedad.direccion}, Piso ${propiedad.piso}, ${propiedad.habitaciones} hab, ${propiedad.superficie}m²`
      }
    }
    
    const formatPrice = (precio) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(precio)
    }
    
    const getEstadoClass = (estado) => {
      const classes = {
        'disponible': 'text-success',
        'ocupado': 'text-danger',
        'mantenimiento': 'text-warning'
      }
      return classes[estado] || 'text-muted'
    }
    
    const loadPropiedades = async () => {
      if (!tipoPropiedad.value) return
      
      isLoading.value = true
      try {
        if (tipoPropiedad.value === 'trastero') {
          const response = await trasterosService.getAll()
          trasteros.value = response.data
        } else {
          const response = await pisosService.getAll()
          pisos.value = response.data
        }
      } catch (error) {
        console.error('Error al cargar propiedades:', error)
        toast.error(`Error al cargar la lista de ${tipoPropiedad.value}s`)
      } finally {
        isLoading.value = false
      }
    }
    
    const handleTipoChange = () => {
      selectedPropiedad.value = null
      updateModelValue()
      loadPropiedades()
    }
    
    const handleChange = () => {
      updateModelValue()
      emit('change', {
        tipo: tipoPropiedad.value,
        id: selectedPropiedad.value,
        data: selectedPropiedadData.value
      })
    }
    
    const updateModelValue = () => {
      emit('update:modelValue', {
        tipo: tipoPropiedad.value,
        id: selectedPropiedad.value
      })
    }
    
    // Cargar propiedades cuando cambia el tipo
    watch(tipoPropiedad, () => {
      if (tipoPropiedad.value) {
        loadPropiedades()
      }
    })
    
    // Sincronizar con modelValue externo
    watch(() => props.modelValue, (newValue) => {
      if (newValue) {
        tipoPropiedad.value = newValue.tipo || ''
        selectedPropiedad.value = newValue.id || null
      }
    }, { deep: true })
    
    return {
      tipoPropiedad,
      selectedPropiedad,
      filteredPropiedades,
      selectedPropiedadData,
      isLoading,
      getPlaceholder,
      formatPropiedadOption,
      formatPropiedadInfo,
      formatPrice,
      getEstadoClass,
      loadPropiedades,
      handleTipoChange,
      handleChange
    }
  }
}
</script>

<style scoped>
.propiedad-selector .input-group {
  flex-wrap: nowrap;
}

.propiedad-selector .form-select {
  min-width: 0;
}

.propiedad-selector .btn {
  border-left: 0;
}

.propiedad-selector .btn:first-of-type {
  border-left: 1px solid #ced4da;
}

.propiedad-selector .card {
  border: 1px solid #e9ecef;
  font-size: 0.875rem;
}
</style>

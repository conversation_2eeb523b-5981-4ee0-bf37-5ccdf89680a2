<template>
  <div class="trastero-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Añadir' }} Trastero</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del trastero' : 'Registra un nuevo trastero en el sistema' }}</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Número</label>
                    <input v-model="form.num" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Número de Planta</label>
                    <input v-model="form.numero_planta" type="text" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Título</label>
                    <input v-model="form.titulo" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Tipo de Trastero</label>
                    <input v-model="form.tipo_trastero" type="text" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Metros Cuadrados</label>
                    <input v-model="form.metros_cuadrados" type="number" step="0.01" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Precio (€/mes)</label>
                    <input v-model="form.price" type="number" step="0.01" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Estado</label>
                    <select v-model="form.ocupado" class="form-select" required>
                      <option value="disponible">Disponible</option>
                      <option value="ocupado">Ocupado</option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Descripción</label>
                    <textarea v-model="form.descripcion" class="form-control" rows="3"></textarea>
                  </div>
                </div>
                
                <div class="d-flex justify-content-end mt-4">
                  <router-link to="/trasteros" class="btn btn-secondary me-2">Cancelar</router-link>
                  <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                    <i class="fas fa-save me-1"></i> {{ isSubmitting ? 'Guardando...' : 'Guardar' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'vue-toastification'
import { trasterosService } from '@/services/trasteros'

export default {
  name: 'TrasteroForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const form = ref({
      num: '',
      titulo: '',
      descripcion: '',
      metros_cuadrados: '',
      price: '',
      ocupado: '',
      tipo_trastero: '',
      numero_planta: ''
    })
    
    const isEdit = computed(() => !!props.id)
  
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await trasterosService.getById(props.id);

          console.log('response', response);

          const trastero = response.data.trastero;

          form.value = {
            num: trastero.num,
            titulo: trastero.titulo,
            descripcion: trastero.descripcion,
            metros_cuadrados: trastero.metros_cuadrados,
            price: trastero.price,
            ocupado: trastero.ocupado,
            tipo_trastero: trastero.tipo_trastero,
            numero_planta: trastero.numero_planta
          };
        } catch (error) {
          console.error('Error al cargar trastero:', error);
          toast.error('Error al cargar los datos del trastero');
        }
      }
    })
    
    const handleSubmit = async () => {
      isSubmitting.value = true
      
      try {
        if (isEdit.value) {
          
          await trasterosService.update(props.id, form.value)

          toast.success('Trastero actualizado correctamente')
        } else {
          await trasterosService.create(form.value)
          toast.success('Trastero creado correctamente')
        }
        router.push('/trasteros')
      } catch (error) {
        const message = error.response?.data?.message || 'Error al guardar el trastero'
        toast.error(message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isEdit,
      isSubmitting,
      handleSubmit
    }
  }
}
</script>





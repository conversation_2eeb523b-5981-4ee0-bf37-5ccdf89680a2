<template>
  <div class="cliente-form">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">
                <i class="fas fa-user me-2"></i>
                {{ isEdit ? 'Editar' : 'Nuevo' }} Cliente
              </h1>
              <p class="text-muted mb-0">
                {{ isEdit ? 'Modifica los datos del cliente' : 'Registra un nuevo cliente en el sistema' }}
              </p>
            </div>
            <router-link to="/clientes" class="btn btn-outline-secondary">
              <i class="fas fa-arrow-left me-1"></i>Volver al listado
            </router-link>
          </div>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-user-edit me-2"></i>
                Información del Cliente
              </h5>
            </div>
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Nombre *</label>
                    <input 
                      v-model="form.nombre" 
                      type="text" 
                      class="form-control"
                      required
                    >
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Apellidos *</label>
                    <input 
                      v-model="form.apellido" 
                      type="text" 
                      class="form-control"
                      required
                    >
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">DNI/NIE *</label>
                    <input 
                      v-model="form.documento" 
                      type="text" 
                      class="form-control"
                      required
                    >
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Teléfono *</label>
                    <input 
                      v-model="form.telefono" 
                      type="tel" 
                      class="form-control"
                      required
                    >
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Email *</label>
                    <input 
                      v-model="form.email" 
                      type="email" 
                      class="form-control"
                      required
                    >
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Estado *</label>
                    <select 
                      v-model="form.estado" 
                      class="form-select"
                      required
                    >
                      <option value="">Selecciona un estado</option>
                      <option value="posible">Posible Cliente</option>
                      <option value="confirmado">Cliente Confirmado</option>
                      <option value="activo">Cliente Activo</option>
                      <option value="inactivo">Cliente Inactivo</option>
                    </select>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Dirección</label>
                    <textarea 
                      v-model="form.direccion" 
                      class="form-control"
                      rows="2"
                    ></textarea>
                  </div>
                </div>
                
                <div class="row mb-4">
                  <div class="col-12">
                    <label class="form-label">Notas</label>
                    <textarea 
                      v-model="form.notas" 
                      class="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                </div>

                <!-- Botones de acción -->
                <div class="d-flex justify-content-end">
                  <router-link to="/clientes" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-times me-1"></i>Cancelar
                  </router-link>
                  <button 
                    type="submit" 
                    class="btn btn-primary" 
                    :disabled="isSubmitting"
                  >
                    <i class="fas fa-save me-1"></i>
                    {{ isSubmitting ? 'Guardando...' : (isEdit ? 'Actualizar Cliente' : 'Crear Cliente') }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { clientesService } from '@/services/clientes'

export default {
  name: 'ClienteForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const form = ref({
      nombre: '',
      apellido: '',
      documento: '',
      telefono: '',
      email: '',
      direccion: '',
      estado: '',
      notas: ''
    })
    
    const isEdit = computed(() => !!props.id)
    
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await clientesService.getById(props.id)

          form.value = { ...form.value, ...response.data.cliente }
        } catch (error) {
          console.error('Error al cargar cliente:', error)
          toast.error('Error al cargar los datos del cliente')
          router.push('/clientes')
        }
      }
    })
    
    const handleSubmit = async () => {
      isSubmitting.value = true
      
      try {
        const clienteData = {
          ...form.value,
          nombre: form.value.nombre.trim(),
          apellido: form.value.apellido.trim(),
          documento: form.value.documento.trim().toUpperCase(),
          telefono: form.value.telefono.trim(),
          email: form.value.email.trim().toLowerCase(),
          direccion: form.value.direccion.trim(),
          notas: form.value.notas.trim()
        }
        
        if (isEdit.value) {
          await clientesService.update(props.id, clienteData)
          toast.success('Cliente actualizado correctamente')
        } else {
          await clientesService.create(clienteData)
          toast.success('Cliente creado correctamente')
        }
        router.push('/clientes')
      } catch (error) {
        console.error('Error al guardar cliente:', error)
        const message = error.response?.data?.message || 'Error al guardar el cliente'
        toast.error(message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isEdit,
      isSubmitting,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.form-label {
  font-weight: 500;
  color: #5a5c69;
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.btn-primary {
  background-color: #4e73df;
  border-color: #4e73df;
}

.btn-primary:hover {
  background-color: #2e59d9;
  border-color: #2653d4;
}
</style>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CORS - BarnaTrasteros</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test CORS - BarnaTrasteros API</h1>
    
    <div class="test-section">
        <h3>1. Test Conexión Básica</h3>
        <button onclick="testBasicConnection()">Probar Conexión</button>
        <div id="basic-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test CSRF Token</h3>
        <button onclick="testCSRFToken()">Obtener CSRF Token</button>
        <div id="csrf-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Login</h3>
        <button onclick="testLogin()">Probar Login</button>
        <div id="login-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000/api';

        async function testBasicConnection() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = '<p>Probando conexión...</p>';

            try {
                const response = await fetch(`${API_BASE_URL}/csrf-token`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Conexión exitosa</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error de conexión</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testCSRFToken() {
            const resultDiv = document.getElementById('csrf-result');
            resultDiv.innerHTML = '<p>Obteniendo CSRF token...</p>';

            try {
                const response = await fetch(`${API_BASE_URL}/csrf-token`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    window.csrfToken = data.csrf_token;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ CSRF Token obtenido</h4>
                            <p>Token: ${data.csrf_token}</p>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error obteniendo CSRF token</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>Probando login...</p>';

            try {
                const response = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': window.csrfToken || '',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login exitoso</h4>
                            <p>Usuario: ${data.user.name}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Error en login</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error en login</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

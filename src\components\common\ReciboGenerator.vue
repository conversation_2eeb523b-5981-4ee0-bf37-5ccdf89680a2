<template>
  <div class="recibo-generator">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-receipt me-2"></i>Generar Recibo
        </h5>
      </div>
      <div class="card-body">
        <!-- Información del recibo -->
        <div class="row mb-3">
          <div class="col-md-6">
            <label class="form-label">Número de recibo</label>
            <input 
              v-model="form.numero_recibo" 
              type="text" 
              class="form-control" 
              :class="{ 'is-invalid': errors.numero_recibo }"
              placeholder="Se generará automáticamente"
              readonly
            >
            <div v-if="errors.numero_recibo" class="invalid-feedback">
              {{ errors.numero_recibo }}
            </div>
          </div>
          <div class="col-md-6">
            <label class="form-label">Fecha de emisión</label>
            <input 
              v-model="form.fecha_emision" 
              type="date" 
              class="form-control" 
              :class="{ 'is-invalid': errors.fecha_emision }"
              :max="today"
            >
            <div v-if="errors.fecha_emision" class="invalid-feedback">
              {{ errors.fecha_emision }}
            </div>
          </div>
        </div>
        
        <!-- Datos del pagador -->
        <div class="row mb-3">
          <div class="col-12">
            <h6 class="fw-bold mb-2">Datos del Pagador</h6>
          </div>
          <div class="col-md-6">
            <label class="form-label">Nombre completo</label>
            <input 
              v-model="form.pagador_nombre" 
              type="text" 
              class="form-control" 
              :class="{ 'is-invalid': errors.pagador_nombre }"
              required
            >
            <div v-if="errors.pagador_nombre" class="invalid-feedback">
              {{ errors.pagador_nombre }}
            </div>
          </div>
          <div class="col-md-6">
            <label class="form-label">DNI/NIE</label>
            <input 
              v-model="form.pagador_dni" 
              type="text" 
              class="form-control" 
              :class="{ 'is-invalid': errors.pagador_dni }"
              required
            >
            <div v-if="errors.pagador_dni" class="invalid-feedback">
              {{ errors.pagador_dni }}
            </div>
          </div>
        </div>
        
        <!-- Concepto del pago -->
        <div class="row mb-3">
          <div class="col-12">
            <label class="form-label">Concepto del pago</label>
            <textarea 
              v-model="form.concepto" 
              class="form-control" 
              :class="{ 'is-invalid': errors.concepto }"
              rows="2"
              placeholder="Ej: Alquiler correspondiente al mes de..."
              required
            ></textarea>
            <div v-if="errors.concepto" class="invalid-feedback">
              {{ errors.concepto }}
            </div>
          </div>
        </div>
        
        <!-- Detalles del pago -->
        <div class="row mb-3">
          <div class="col-md-4">
            <PriceInput
              v-model="form.importe"
              label="Importe"
              :error="errors.importe"
              required
            />
          </div>
          <div class="col-md-4">
            <label class="form-label">Método de pago</label>
            <select 
              v-model="form.metodo_pago" 
              class="form-select" 
              :class="{ 'is-invalid': errors.metodo_pago }"
              required
            >
              <option value="">Selecciona...</option>
              <option value="efectivo">Efectivo</option>
              <option value="transferencia">Transferencia bancaria</option>
              <option value="tarjeta">Tarjeta</option>
              <option value="cheque">Cheque</option>
              <option value="bizum">Bizum</option>
              <option value="otro">Otro</option>
            </select>
            <div v-if="errors.metodo_pago" class="invalid-feedback">
              {{ errors.metodo_pago }}
            </div>
          </div>
          <div class="col-md-4">
            <label class="form-label">Referencia</label>
            <input 
              v-model="form.referencia" 
              type="text" 
              class="form-control" 
              placeholder="Opcional"
            >
          </div>
        </div>
        
        <!-- Período que cubre -->
        <div class="row mb-3">
          <div class="col-12">
            <DateRangePicker
              v-model="form.periodo"
              start-label="Período desde"
              end-label="Período hasta"
              :show-quick-buttons="true"
              :show-range-info="true"
              help-text="Período que cubre este pago"
              @validation-change="handlePeriodoValidation"
            />
          </div>
        </div>
        
        <!-- Observaciones -->
        <div class="row mb-3">
          <div class="col-12">
            <label class="form-label">Observaciones</label>
            <textarea 
              v-model="form.observaciones" 
              class="form-control" 
              rows="2"
              placeholder="Observaciones adicionales (opcional)"
            ></textarea>
          </div>
        </div>
        
        <!-- Preview del recibo -->
        <div v-if="showPreview" class="row mb-3">
          <div class="col-12">
            <div class="card bg-light">
              <div class="card-header">
                <h6 class="mb-0">Vista previa del recibo</h6>
              </div>
              <div class="card-body">
                <div class="recibo-preview">
                  <div class="text-center mb-3">
                    <h5>RECIBO DE PAGO</h5>
                    <p class="mb-1">Nº {{ form.numero_recibo || 'XXXX' }}</p>
                    <p class="text-muted">{{ formatFecha(form.fecha_emision) }}</p>
                  </div>
                  
                  <div class="row mb-3">
                    <div class="col-6">
                      <strong>Pagador:</strong><br>
                      {{ form.pagador_nombre }}<br>
                      DNI: {{ form.pagador_dni }}
                    </div>
                    <div class="col-6 text-end">
                      <strong>Importe:</strong><br>
                      <span class="fs-4 text-primary">{{ formatPrecio(form.importe) }}</span>
                    </div>
                  </div>
                  
                  <div class="mb-3">
                    <strong>Concepto:</strong><br>
                    {{ form.concepto }}
                  </div>
                  
                  <div v-if="form.periodo.start && form.periodo.end" class="mb-3">
                    <strong>Período:</strong> 
                    {{ formatFecha(form.periodo.start) }} - {{ formatFecha(form.periodo.end) }}
                  </div>
                  
                  <div class="row">
                    <div class="col-6">
                      <strong>Método de pago:</strong> {{ getMetodoPagoText() }}
                      <div v-if="form.referencia">
                        <strong>Referencia:</strong> {{ form.referencia }}
                      </div>
                    </div>
                    <div class="col-6 text-end">
                      <small class="text-muted">
                        Recibo generado el {{ formatFecha(today) }}
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Botones de acción -->
        <div class="d-flex justify-content-between">
          <button 
            type="button" 
            class="btn btn-outline-secondary"
            @click="togglePreview"
          >
            <i class="fas fa-eye me-1"></i>
            {{ showPreview ? 'Ocultar' : 'Vista previa' }}
          </button>
          
          <div class="btn-group">
            <button 
              type="button" 
              class="btn btn-outline-primary"
              @click="generatePDF"
              :disabled="!isFormValid || isGenerating"
            >
              <i class="fas fa-file-pdf me-1"></i>
              {{ isGenerating ? 'Generando...' : 'Generar PDF' }}
            </button>
            <button 
              type="button" 
              class="btn btn-primary"
              @click="saveAndGenerate"
              :disabled="!isFormValid || isGenerating"
            >
              <i class="fas fa-save me-1"></i>
              Guardar y generar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useToast } from 'vue-toastification'
import PriceInput from './PriceInput.vue'
import DateRangePicker from './DateRangePicker.vue'
import moment from 'moment'

export default {
  name: 'ReciboGenerator',
  components: {
    PriceInput,
    DateRangePicker
  },
  props: {
    alquiler: {
      type: Object,
      default: null
    },
    pago: {
      type: Object,
      default: null
    }
  },
  emits: ['recibo-generated', 'recibo-saved'],
  setup(props, { emit }) {
    const toast = useToast()
    
    const isGenerating = ref(false)
    const showPreview = ref(false)
    const errors = ref({})
    const periodoValidation = ref({ isValid: true, errors: {} })
    
    const form = ref({
      numero_recibo: '',
      fecha_emision: moment().format('YYYY-MM-DD'),
      pagador_nombre: '',
      pagador_dni: '',
      concepto: '',
      importe: null,
      metodo_pago: '',
      referencia: '',
      periodo: { start: null, end: null },
      observaciones: ''
    })
    
    const today = computed(() => moment().format('YYYY-MM-DD'))
    
    const isFormValid = computed(() => {
      return form.value.pagador_nombre && 
             form.value.pagador_dni && 
             form.value.concepto && 
             form.value.importe && 
             form.value.metodo_pago &&
             periodoValidation.value.isValid &&
             Object.keys(errors.value).length === 0
    })
    
    const formatFecha = (fecha) => {
      return fecha ? moment(fecha).format('DD/MM/YYYY') : ''
    }

    const formatPrecio = (precio) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(precio || 0)
    }

    const getMetodoPagoText = () => {
      const metodos = {
        'efectivo': 'Efectivo',
        'transferencia': 'Transferencia bancaria',
        'tarjeta': 'Tarjeta',
        'cheque': 'Cheque',
        'bizum': 'Bizum',
        'otro': 'Otro'
      }
      return metodos[form.value.metodo_pago] || form.value.metodo_pago
    }

    const handlePeriodoValidation = (validation) => {
      periodoValidation.value = validation
    }

    const togglePreview = () => {
      showPreview.value = !showPreview.value
    }

    const validateForm = () => {
      errors.value = {}

      if (!form.value.pagador_nombre?.trim()) {
        errors.value.pagador_nombre = 'El nombre del pagador es obligatorio'
      }

      if (!form.value.pagador_dni?.trim()) {
        errors.value.pagador_dni = 'El DNI del pagador es obligatorio'
      }

      if (!form.value.concepto?.trim()) {
        errors.value.concepto = 'El concepto del pago es obligatorio'
      }

      if (!form.value.importe || form.value.importe <= 0) {
        errors.value.importe = 'El importe debe ser mayor a 0'
      }

      if (!form.value.metodo_pago) {
        errors.value.metodo_pago = 'Debe seleccionar el método de pago'
      }

      return Object.keys(errors.value).length === 0 && periodoValidation.value.isValid
    }

    const generatePDF = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isGenerating.value = true

      try {
        // Aquí iría la lógica para generar el PDF
        // Por ahora simulamos la generación
        await new Promise(resolve => setTimeout(resolve, 2000))

        const reciboData = {
          ...form.value,
          fecha_generacion: moment().toISOString()
        }

        emit('recibo-generated', reciboData)
        toast.success('Recibo generado correctamente')
      } catch (error) {
        console.error('Error al generar recibo:', error)
        toast.error('Error al generar el recibo')
      } finally {
        isGenerating.value = false
      }
    }

    const saveAndGenerate = async () => {
      if (!validateForm()) {
        toast.error('Por favor, corrige los errores en el formulario')
        return
      }

      isGenerating.value = true

      try {
        const reciboData = {
          ...form.value,
          fecha_generacion: moment().toISOString()
        }

        emit('recibo-saved', reciboData)
        await generatePDF()
      } catch (error) {
        console.error('Error al guardar y generar recibo:', error)
        toast.error('Error al procesar el recibo')
      } finally {
        isGenerating.value = false
      }
    }

    // Inicializar datos si se proporcionan props
    watch(() => props.alquiler, (alquiler) => {
      if (alquiler) {
        form.value.pagador_nombre = `${alquiler.cliente?.nombre || ''} ${alquiler.cliente?.apellidos || ''}`.trim()
        form.value.pagador_dni = alquiler.cliente?.dni || ''
        form.value.importe = alquiler.valor

        // Generar concepto automático
        const propiedad = alquiler.tipo_propiedad === 'App\\Trastero' ? 'trastero' : 'piso'
        const mes = moment().format('MMMM YYYY')
        form.value.concepto = `Alquiler de ${propiedad} correspondiente al mes de ${mes}`
      }
    }, { immediate: true })

    watch(() => props.pago, (pago) => {
      if (pago) {
        form.value.importe = pago.importe
        form.value.metodo_pago = pago.metodo_pago
        form.value.referencia = pago.referencia
        form.value.fecha_emision = pago.fecha_pago
        form.value.observaciones = pago.observaciones

        if (pago.periodo_inicio && pago.periodo_fin) {
          form.value.periodo = {
            start: pago.periodo_inicio,
            end: pago.periodo_fin
          }
        }
      }
    }, { immediate: true })

    return {
      form,
      errors,
      today,
      isGenerating,
      showPreview,
      isFormValid,
      periodoValidation,
      formatFecha,
      formatPrecio,
      getMetodoPagoText,
      handlePeriodoValidation,
      togglePreview,
      generatePDF,
      saveAndGenerate
    }
  }
}
</script>

<style scoped>
.recibo-generator .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.recibo-generator .card-title {
  color: #495057;
  font-weight: 600;
}

.recibo-preview {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  background: white;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.recibo-preview h5 {
  font-weight: bold;
  text-decoration: underline;
}

.recibo-generator .btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.recibo-generator .form-control:focus,
.recibo-generator .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.recibo-generator .is-invalid {
  border-color: #dc3545;
}

.recibo-generator .invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.recibo-generator .bg-light {
  background-color: #f8f9fa !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .recibo-generator .card-body {
    padding: 1rem;
  }

  .recibo-generator .btn-group {
    flex-direction: column;
    width: 100%;
  }

  .recibo-generator .btn-group .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .recibo-preview {
    padding: 1rem;
    font-size: 0.8rem;
  }
}

/* Print styles */
@media print {
  .recibo-generator .card-header,
  .recibo-generator .btn,
  .recibo-generator .form-control,
  .recibo-generator .form-select {
    display: none !important;
  }

  .recibo-preview {
    border: none;
    box-shadow: none;
    padding: 0;
  }
}
</style>

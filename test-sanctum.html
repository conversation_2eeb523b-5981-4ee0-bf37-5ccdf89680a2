<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Laravel Sanctum - BarnaTrasteros</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Laravel Sanctum - BarnaTrasteros API</h1>
    
    <div class="test-section">
        <h3>1. Test Inicialización Sanctum</h3>
        <button onclick="testSanctumInit()">Inicializar Sanctum</button>
        <div id="sanctum-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Login con Sanctum</h3>
        <button onclick="testLogin()">Probar Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Petición Autenticada</h3>
        <button onclick="testAuthenticatedRequest()">Probar Petición con Token</button>
        <div id="auth-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        const SANCTUM_URL = 'http://localhost:8000/sanctum/csrf-cookie';
        let authToken = null;

        async function testSanctumInit() {
            const resultDiv = document.getElementById('sanctum-result');
            resultDiv.innerHTML = '<p>Inicializando Sanctum...</p>';

            try {
                const response = await fetch(SANCTUM_URL, {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Sanctum inicializado correctamente</h4>
                            <p>Status: ${response.status}</p>
                            <p>Cookies establecidas correctamente</p>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error inicializando Sanctum</h4>
                        <p>${error.message}</p>
                        <p><strong>Posibles soluciones:</strong></p>
                        <ul>
                            <li>Verificar que el backend esté ejecutándose en localhost:8000</li>
                            <li>Verificar configuración de CORS en Laravel</li>
                            <li>Verificar SANCTUM_STATEFUL_DOMAINS en .env del backend</li>
                        </ul>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<p>Probando login...</p>';

            try {
                // Primero inicializar Sanctum
                await fetch(SANCTUM_URL, {
                    method: 'GET',
                    credentials: 'include'
                });

                // Luego hacer login
                const response = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.token;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Login exitoso</h4>
                            <p>Usuario: ${data.user.name}</p>
                            <p>Email: ${data.user.email}</p>
                            <p>Token: ${data.token.substring(0, 20)}...</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Error en login</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error de conexión en login</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAuthenticatedRequest() {
            const resultDiv = document.getElementById('auth-result');
            
            if (!authToken) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ No hay token de autenticación</h4>
                        <p>Primero debes hacer login exitosamente</p>
                    </div>
                `;
                return;
            }

            resultDiv.innerHTML = '<p>Probando petición autenticada...</p>';

            try {
                const response = await fetch(`${API_BASE_URL}/user`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    credentials: 'include'
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Petición autenticada exitosa</h4>
                            <p>Usuario autenticado: ${data.name}</p>
                            <p>Email: ${data.email}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Error en petición autenticada</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Error de conexión</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

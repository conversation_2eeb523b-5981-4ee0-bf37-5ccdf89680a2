// Constantes de la aplicación

// Estados de pago
export const ESTADOS_PAGO = {
  PENDIENTE: 'pendiente',
  AL_DIA: 'al_dia',
  VENCIDO: 'vencido',
  PARCIAL: 'parcial'
}

// Estados de cliente
export const ESTADOS_CLIENTE = {
  ACTIVO: 'activo',
  POSIBLE: 'posible',
  INACTIVO: 'inactivo'
}

// Estados de documento
export const ESTADOS_DOCUMENTO = {
  PENDIENTE: 'pendiente',
  PROCESADO: 'procesado',
  ARCHIVADO: 'archivado'
}

// Tipos de documento
export const TIPOS_DOCUMENTO = {
  CONTRATO: 'contrato',
  RECIBO: 'recibo',
  FACTURA: 'factura',
  DOCUMENTO_IDENTIDAD: 'documento_identidad',
  JUSTIFICANTE_PAGO: 'justificante_pago',
  OTRO: 'otro'
}

// Tipos de gasto
export const TIPOS_GASTO = {
  MANTENIMIENTO: 'mantenimiento',
  LIMPIEZA: 'limpieza',
  SEGURIDAD: 'seguridad',
  SUMINISTROS: 'suministros',
  ADMINISTRACION: 'administracion',
  REPARACIONES: 'reparaciones',
  OTRO: 'otro'
}

// Frecuencias de gasto recurrente
export const FRECUENCIAS_GASTO = {
  MENSUAL: 'mensual',
  TRIMESTRAL: 'trimestral',
  SEMESTRAL: 'semestral',
  ANUAL: 'anual'
}

// Tipos de propiedad
export const TIPOS_PROPIEDAD = {
  TRASTERO: 'App\\Trastero',
  PISO: 'App\\Piso'
}

// Tamaños de archivo permitidos
export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

// Tipos de archivo permitidos
export const ALLOWED_FILE_TYPES = [
  'pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'
]

// Configuración de paginación
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 15,
  PAGE_SIZE_OPTIONS: [10, 15, 25, 50, 100],
  MAX_PAGE_SIZE: 100
}

// Configuración de notificaciones
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
}

// Duración de notificaciones (ms)
export const NOTIFICATION_DURATION = {
  SUCCESS: 3000,
  ERROR: 5000,
  WARNING: 4000,
  INFO: 3000
}

// Colores del tema
export const THEME_COLORS = {
  PRIMARY: '#007bff',
  SECONDARY: '#6c757d',
  SUCCESS: '#28a745',
  DANGER: '#dc3545',
  WARNING: '#ffc107',
  INFO: '#17a2b8',
  LIGHT: '#f8f9fa',
  DARK: '#343a40'
}

// Breakpoints responsivos
export const BREAKPOINTS = {
  XS: 0,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1400
}

// Configuración de gráficos
export const CHART_COLORS = [
  '#007bff', '#28a745', '#ffc107', '#dc3545', 
  '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'
]

// Configuración de API
export const API_CONFIG = {
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000
}

// Rutas de la aplicación
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  TRASTEROS: '/trasteros',
  PISOS: '/pisos',
  CLIENTES: '/clientes',
  DOCUMENTOS: '/documentos',
  GASTOS: '/gastos',
  ALQUILERES: '/alquileres',
  REPORTES: '/reportes'
}

// Mensajes de error comunes
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Error de conexión. Verifique su conexión a internet.',
  UNAUTHORIZED: 'No tiene permisos para realizar esta acción.',
  NOT_FOUND: 'El recurso solicitado no fue encontrado.',
  SERVER_ERROR: 'Error interno del servidor. Intente nuevamente.',
  VALIDATION_ERROR: 'Los datos ingresados no son válidos.',
  TIMEOUT_ERROR: 'La operación tardó demasiado tiempo. Intente nuevamente.'
}

// Mensajes de éxito
export const SUCCESS_MESSAGES = {
  CREATED: 'Registro creado exitosamente.',
  UPDATED: 'Registro actualizado exitosamente.',
  DELETED: 'Registro eliminado exitosamente.',
  SAVED: 'Cambios guardados exitosamente.',
  UPLOADED: 'Archivo subido exitosamente.',
  SENT: 'Enviado exitosamente.'
}

// Configuración de localStorage
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  PREFERENCES: 'user_preferences',
  THEME: 'app_theme'
}

// Configuración de fechas
export const DATE_FORMATS = {
  SHORT: 'DD/MM/YYYY',
  LONG: 'DD/MM/YYYY HH:mm',
  ISO: 'YYYY-MM-DD',
  DISPLAY: 'DD [de] MMMM [de] YYYY'
}

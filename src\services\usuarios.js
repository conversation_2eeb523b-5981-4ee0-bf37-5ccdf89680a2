import api from './api'

export const usuariosService = {
  // Obtener todos los usuarios
  async getAll() {
    try {
      const response = await api.get('/usuarios')
      return response
    } catch (error) {
      console.error('Error al obtener usuarios:', error)
      throw error
    }
  },

  // Obtener un usuario por ID
  async getById(id) {
    try {
      const response = await api.get(`/usuarios/${id}`)
      return response
    } catch (error) {
      console.error('Error al obtener usuario:', error)
      throw error
    }
  },

  // Crear un nuevo usuario
  async create(userData) {
    try {
      const response = await api.post('/usuarios', userData)
      return response
    } catch (error) {
      console.error('Error al crear usuario:', error)
      throw error
    }
  },

  // Actualizar un usuario
  async update(id, userData) {
    try {
      const response = await api.put(`/usuarios/${id}`, userData)
      return response
    } catch (error) {
      console.error('Error al actualizar usuario:', error)
      throw error
    }
  },

  // Actualizar solo el estado de un usuario
  async updateEstado(id, estado) {
    try {
      const response = await api.patch(`/usuarios/${id}/estado`, { estado })
      return response
    } catch (error) {
      console.error('Error al actualizar estado del usuario:', error)
      throw error
    }
  },

  // Eliminar un usuario
  async delete(id) {
    try {
      const response = await api.delete(`/usuarios/${id}`)
      return response
    } catch (error) {
      console.error('Error al eliminar usuario:', error)
      throw error
    }
  },

  // Cambiar contraseña de un usuario
  async changePassword(id, passwordData) {
    try {
      const response = await api.patch(`/usuarios/${id}/password`, passwordData)
      return response
    } catch (error) {
      console.error('Error al cambiar contraseña:', error)
      throw error
    }
  },

  // Obtener usuarios por rol
  async getByRole(rol) {
    try {
      const response = await api.get(`/usuarios?rol=${rol}`)
      return response
    } catch (error) {
      console.error('Error al obtener usuarios por rol:', error)
      throw error
    }
  },

  // Buscar usuarios
  async search(query) {
    try {
      const response = await api.get(`/usuarios/search?q=${encodeURIComponent(query)}`)
      return response
    } catch (error) {
      console.error('Error al buscar usuarios:', error)
      throw error
    }
  },

  // Obtener estadísticas de usuarios
  async getStats() {
    try {
      const response = await api.get('/usuarios/stats')
      return response
    } catch (error) {
      console.error('Error al obtener estadísticas de usuarios:', error)
      throw error
    }
  },

  // Exportar usuarios
  async export(format = 'csv') {
    try {
      const response = await api.get(`/usuarios/export?format=${format}`, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error al exportar usuarios:', error)
      throw error
    }
  },

  // Importar usuarios
  async import(file) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await api.post('/usuarios/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response
    } catch (error) {
      console.error('Error al importar usuarios:', error)
      throw error
    }
  },

  // Verificar disponibilidad de username
  async checkUsername(username) {
    try {
      const response = await api.get(`/usuarios/check-username?username=${encodeURIComponent(username)}`)
      return response
    } catch (error) {
      console.error('Error al verificar username:', error)
      throw error
    }
  },

  // Verificar disponibilidad de email
  async checkEmail(email) {
    try {
      const response = await api.get(`/usuarios/check-email?email=${encodeURIComponent(email)}`)
      return response
    } catch (error) {
      console.error('Error al verificar email:', error)
      throw error
    }
  },

  // Obtener permisos de un usuario
  async getPermissions(id) {
    try {
      const response = await api.get(`/usuarios/${id}/permissions`)
      return response
    } catch (error) {
      console.error('Error al obtener permisos del usuario:', error)
      throw error
    }
  },

  // Actualizar permisos de un usuario
  async updatePermissions(id, permissions) {
    try {
      const response = await api.put(`/usuarios/${id}/permissions`, { permissions })
      return response
    } catch (error) {
      console.error('Error al actualizar permisos del usuario:', error)
      throw error
    }
  },

  // Obtener historial de actividad de un usuario
  async getActivityLog(id, params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString()
      const response = await api.get(`/usuarios/${id}/activity?${queryString}`)
      return response
    } catch (error) {
      console.error('Error al obtener historial de actividad:', error)
      throw error
    }
  },

  // Resetear contraseña de un usuario
  async resetPassword(id) {
    try {
      const response = await api.post(`/usuarios/${id}/reset-password`)
      return response
    } catch (error) {
      console.error('Error al resetear contraseña:', error)
      throw error
    }
  },

  // Enviar invitación a un usuario
  async sendInvitation(id) {
    try {
      const response = await api.post(`/usuarios/${id}/send-invitation`)
      return response
    } catch (error) {
      console.error('Error al enviar invitación:', error)
      throw error
    }
  }
}

export default usuariosService
